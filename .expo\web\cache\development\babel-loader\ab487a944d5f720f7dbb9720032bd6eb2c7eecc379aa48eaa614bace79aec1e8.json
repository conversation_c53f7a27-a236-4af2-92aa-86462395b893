{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport Dimensions from \"react-native-web/dist/exports/Dimensions\";\nimport { Text, Card, Title, Paragraph, Button, FAB, Chip, Surface, useTheme } from 'react-native-paper';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar mockProducts = [{\n  id: '1',\n  name: 'حليب نادك',\n  barcode: '123456789',\n  expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n  status: 'warning',\n  location: 'fridge',\n  quantity: 2,\n  category: 'dairy',\n  addedAt: new Date()\n}, {\n  id: '2',\n  name: 'خبز التميس',\n  barcode: '987654321',\n  expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n  status: 'fresh',\n  location: 'counter',\n  quantity: 1,\n  category: 'bakery',\n  addedAt: new Date()\n}, {\n  id: '3',\n  name: 'زبادي المراعي',\n  barcode: '456789123',\n  expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n  status: 'expired',\n  location: 'fridge',\n  quantity: 3,\n  category: 'dairy',\n  addedAt: new Date()\n}];\nvar _Dimensions$get = Dimensions.get('window'),\n  width = _Dimensions$get.width;\nvar EnhancedHomeScreen = function EnhancedHomeScreen() {\n  var navigation = useNavigation();\n  var theme = useTheme();\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    refreshing = _useState2[0],\n    setRefreshing = _useState2[1];\n  var getProductStats = function getProductStats() {\n    var total = mockProducts.length;\n    var fresh = mockProducts.filter(function (p) {\n      return p.status === 'fresh';\n    }).length;\n    var warning = mockProducts.filter(function (p) {\n      return p.status === 'warning';\n    }).length;\n    var expired = mockProducts.filter(function (p) {\n      return p.status === 'expired';\n    }).length;\n    return {\n      total: total,\n      fresh: fresh,\n      warning: warning,\n      expired: expired\n    };\n  };\n  var stats = getProductStats();\n  var onRefresh = function () {\n    var _ref = _asyncToGenerator(function* () {\n      setRefreshing(true);\n      yield new Promise(function (resolve) {\n        return setTimeout(resolve, 1000);\n      });\n      setRefreshing(false);\n    });\n    return function onRefresh() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  var getStatusColor = function getStatusColor(status) {\n    switch (status) {\n      case 'fresh':\n        return '#4CAF50';\n      case 'warning':\n        return '#FF9800';\n      case 'expired':\n        return '#F44336';\n      default:\n        return theme.colors.primary;\n    }\n  };\n  var getLocationIcon = function getLocationIcon(location) {\n    switch (location) {\n      case 'fridge':\n        return 'kitchen';\n      case 'freezer':\n        return 'ac-unit';\n      case 'pantry':\n        return 'store';\n      case 'counter':\n        return 'countertops';\n      default:\n        return 'inventory';\n    }\n  };\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    children: [_jsxs(ScrollView, {\n      style: styles.scrollView,\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      children: [_jsxs(Surface, {\n        style: styles.welcomeCard,\n        children: [_jsx(Title, {\n          style: styles.welcomeTitle,\n          children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u0631\\u0641\\u0651\\u0643 \\uD83D\\uDC4B\"\n        }), _jsx(Paragraph, {\n          style: styles.welcomeSubtitle,\n          children: \"\\u062A\\u062A\\u0628\\u0639 \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\\u0643 \\u0648\\u062A\\u0648\\u0627\\u0631\\u064A\\u062E \\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0635\\u0644\\u0627\\u062D\\u064A\\u062A\\u0647\\u0627 \\u0628\\u0633\\u0647\\u0648\\u0644\\u0629\"\n        })]\n      }), _jsxs(View, {\n        style: styles.statsContainer,\n        children: [_jsx(Text, {\n          style: styles.sectionTitle,\n          children: \"\\u0627\\u0644\\u0625\\u062D\\u0635\\u0627\\u0626\\u064A\\u0627\\u062A\"\n        }), _jsxs(View, {\n          style: styles.statsRow,\n          children: [_jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#E8F5E8'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statContent,\n              children: [_jsx(MaterialIcons, {\n                name: \"inventory\",\n                size: 24,\n                color: \"#4CAF50\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: stats.total\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"\\u0625\\u062C\\u0645\\u0627\\u0644\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n              })]\n            })\n          }), _jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#FFF3E0'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statContent,\n              children: [_jsx(MaterialIcons, {\n                name: \"warning\",\n                size: 24,\n                color: \"#FF9800\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: stats.warning\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"\\u062A\\u062D\\u062A\\u0627\\u062C \\u0627\\u0646\\u062A\\u0628\\u0627\\u0647\"\n              })]\n            })\n          }), _jsx(Card, {\n            style: [styles.statCard, {\n              backgroundColor: '#FFEBEE'\n            }],\n            children: _jsxs(Card.Content, {\n              style: styles.statContent,\n              children: [_jsx(MaterialIcons, {\n                name: \"error\",\n                size: 24,\n                color: \"#F44336\"\n              }), _jsx(Text, {\n                style: styles.statNumber,\n                children: stats.expired\n              }), _jsx(Text, {\n                style: styles.statLabel,\n                children: \"\\u0645\\u0646\\u062A\\u0647\\u064A\\u0629 \\u0627\\u0644\\u0635\\u0644\\u0627\\u062D\\u064A\\u0629\"\n              })]\n            })\n          })]\n        })]\n      }), _jsxs(View, {\n        style: styles.recentContainer,\n        children: [_jsx(Text, {\n          style: styles.sectionTitle,\n          children: \"\\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A \\u0627\\u0644\\u062D\\u062F\\u064A\\u062B\\u0629\"\n        }), mockProducts.map(function (product) {\n          return _jsx(Card, {\n            style: styles.productCard,\n            children: _jsxs(Card.Content, {\n              children: [_jsxs(View, {\n                style: styles.productHeader,\n                children: [_jsxs(View, {\n                  style: styles.productInfo,\n                  children: [_jsx(Text, {\n                    style: styles.productName,\n                    children: product.name\n                  }), _jsxs(View, {\n                    style: styles.productMeta,\n                    children: [_jsx(MaterialIcons, {\n                      name: getLocationIcon(product.location),\n                      size: 16,\n                      color: theme.colors.onSurfaceVariant\n                    }), _jsx(Text, {\n                      style: styles.productLocation,\n                      children: product.location === 'fridge' ? 'الثلاجة' : product.location === 'counter' ? 'المنضدة' : 'أخرى'\n                    })]\n                  })]\n                }), _jsx(Chip, {\n                  style: [styles.statusChip, {\n                    backgroundColor: getStatusColor(product.status) + '20'\n                  }],\n                  textStyle: {\n                    color: getStatusColor(product.status)\n                  },\n                  children: product.status === 'fresh' ? 'طازج' : product.status === 'warning' ? 'تحذير' : 'منتهي'\n                })]\n              }), _jsxs(Text, {\n                style: styles.expiryDate,\n                children: [\"\\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0635\\u0644\\u0627\\u062D\\u064A\\u0629: \", product.expiryDate.toLocaleDateString('ar-SA')]\n              })]\n            })\n          }, product.id);\n        })]\n      }), _jsxs(View, {\n        style: styles.quickActions,\n        children: [_jsx(Button, {\n          mode: \"contained\",\n          icon: \"qr-code-scanner\",\n          style: styles.actionButton,\n          onPress: function onPress() {},\n          children: \"\\u0645\\u0633\\u062D \\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\"\n        }), _jsx(Button, {\n          mode: \"outlined\",\n          icon: \"add\",\n          style: styles.actionButton,\n          onPress: function onPress() {},\n          children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u062A\\u062C\"\n        })]\n      })]\n    }), _jsx(FAB, {\n      style: styles.fab,\n      icon: \"add\",\n      onPress: function onPress() {}\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  scrollView: {\n    flex: 1\n  },\n  welcomeCard: {\n    margin: 16,\n    padding: 20,\n    borderRadius: 12,\n    elevation: 2\n  },\n  welcomeTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    textAlign: 'center',\n    marginBottom: 8\n  },\n  welcomeSubtitle: {\n    textAlign: 'center',\n    opacity: 0.7\n  },\n  statsContainer: {\n    margin: 16\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 12\n  },\n  statsRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between'\n  },\n  statCard: {\n    flex: 1,\n    marginHorizontal: 4,\n    borderRadius: 12\n  },\n  statContent: {\n    alignItems: 'center',\n    paddingVertical: 16\n  },\n  statNumber: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginTop: 8\n  },\n  statLabel: {\n    fontSize: 12,\n    textAlign: 'center',\n    marginTop: 4\n  },\n  recentContainer: {\n    margin: 16\n  },\n  productCard: {\n    marginBottom: 8,\n    borderRadius: 8\n  },\n  productHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start'\n  },\n  productInfo: {\n    flex: 1\n  },\n  productName: {\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  productMeta: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 4\n  },\n  productLocation: {\n    marginLeft: 4,\n    fontSize: 12,\n    opacity: 0.7\n  },\n  statusChip: {\n    marginLeft: 8\n  },\n  expiryDate: {\n    fontSize: 12,\n    marginTop: 8,\n    opacity: 0.7\n  },\n  quickActions: {\n    margin: 16,\n    gap: 12\n  },\n  actionButton: {\n    marginBottom: 8\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0\n  }\n});\nexport default EnhancedHomeScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "StyleSheet", "ScrollView", "RefreshControl", "Dimensions", "Text", "Card", "Title", "Paragraph", "<PERSON><PERSON>", "FAB", "Chip", "Surface", "useTheme", "SafeAreaView", "MaterialIcons", "useNavigation", "jsx", "_jsx", "jsxs", "_jsxs", "mockProducts", "id", "name", "barcode", "expiryDate", "Date", "now", "status", "location", "quantity", "category", "addedAt", "_Dimensions$get", "get", "width", "EnhancedHomeScreen", "navigation", "theme", "_useState", "_useState2", "_slicedToArray", "refreshing", "setRefreshing", "getProductStats", "total", "length", "fresh", "filter", "p", "warning", "expired", "stats", "onRefresh", "_ref", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "apply", "arguments", "getStatusColor", "colors", "primary", "getLocationIcon", "style", "styles", "container", "children", "scrollView", "refreshControl", "welcomeCard", "welcomeTitle", "welcomeSubtitle", "stats<PERSON><PERSON><PERSON>", "sectionTitle", "statsRow", "statCard", "backgroundColor", "Content", "statContent", "size", "color", "statNumber", "statLabel", "<PERSON><PERSON><PERSON><PERSON>", "map", "product", "productCard", "productHeader", "productInfo", "productName", "productMeta", "onSurfaceVariant", "productLocation", "statusChip", "textStyle", "toLocaleDateString", "quickActions", "mode", "icon", "actionButton", "onPress", "fab", "create", "flex", "margin", "padding", "borderRadius", "elevation", "fontSize", "fontWeight", "textAlign", "marginBottom", "opacity", "flexDirection", "justifyContent", "marginHorizontal", "alignItems", "paddingVertical", "marginTop", "marginLeft", "gap", "position", "right", "bottom"], "sources": ["C:/Users/<USER>/Music/mobile/src/screens/EnhancedHomeScreen.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  View,\n  StyleSheet,\n  ScrollView,\n  RefreshControl,\n  Dimensions,\n} from 'react-native';\nimport {\n  Text,\n  Card,\n  Title,\n  Paragraph,\n  Button,\n  FAB,\n  Chip,\n  Surface,\n  useTheme,\n} from 'react-native-paper';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { useNavigation } from '@react-navigation/native';\n\n// بيانات وهمية للاختبار\nconst mockProducts = [\n  {\n    id: '1',\n    name: 'حليب نادك',\n    barcode: '123456789',\n    expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // بعد يومين\n    status: 'warning' as const,\n    location: 'fridge' as const,\n    quantity: 2,\n    category: 'dairy',\n    addedAt: new Date(),\n  },\n  {\n    id: '2',\n    name: 'خبز التميس',\n    barcode: '987654321',\n    expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // بعد أسبوع\n    status: 'fresh' as const,\n    location: 'counter' as const,\n    quantity: 1,\n    category: 'bakery',\n    addedAt: new Date(),\n  },\n  {\n    id: '3',\n    name: 'زبادي المراعي',\n    barcode: '456789123',\n    expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // منتهي الصلاحية\n    status: 'expired' as const,\n    location: 'fridge' as const,\n    quantity: 3,\n    category: 'dairy',\n    addedAt: new Date(),\n  },\n];\n\nconst { width } = Dimensions.get('window');\n\nconst EnhancedHomeScreen: React.FC = () => {\n  const navigation = useNavigation();\n  const theme = useTheme();\n  const [refreshing, setRefreshing] = useState(false);\n\n  // إحصائيات المنتجات\n  const getProductStats = () => {\n    const total = mockProducts.length;\n    const fresh = mockProducts.filter(p => p.status === 'fresh').length;\n    const warning = mockProducts.filter(p => p.status === 'warning').length;\n    const expired = mockProducts.filter(p => p.status === 'expired').length;\n    \n    return { total, fresh, warning, expired };\n  };\n\n  const stats = getProductStats();\n\n  const onRefresh = async () => {\n    setRefreshing(true);\n    // محاكاة تحديث البيانات\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    setRefreshing(false);\n  };\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'fresh': return '#4CAF50';\n      case 'warning': return '#FF9800';\n      case 'expired': return '#F44336';\n      default: return theme.colors.primary;\n    }\n  };\n\n  const getLocationIcon = (location: string) => {\n    switch (location) {\n      case 'fridge': return 'kitchen';\n      case 'freezer': return 'ac-unit';\n      case 'pantry': return 'store';\n      case 'counter': return 'countertops';\n      default: return 'inventory';\n    }\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <ScrollView\n        style={styles.scrollView}\n        refreshControl={\n          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />\n        }\n      >\n        {/* ترحيب */}\n        <Surface style={styles.welcomeCard}>\n          <Title style={styles.welcomeTitle}>مرحباً بك في رفّك 👋</Title>\n          <Paragraph style={styles.welcomeSubtitle}>\n            تتبع منتجاتك وتواريخ انتهاء صلاحيتها بسهولة\n          </Paragraph>\n        </Surface>\n\n        {/* إحصائيات سريعة */}\n        <View style={styles.statsContainer}>\n          <Text style={styles.sectionTitle}>الإحصائيات</Text>\n          <View style={styles.statsRow}>\n            <Card style={[styles.statCard, { backgroundColor: '#E8F5E8' }]}>\n              <Card.Content style={styles.statContent}>\n                <MaterialIcons name=\"inventory\" size={24} color=\"#4CAF50\" />\n                <Text style={styles.statNumber}>{stats.total}</Text>\n                <Text style={styles.statLabel}>إجمالي المنتجات</Text>\n              </Card.Content>\n            </Card>\n\n            <Card style={[styles.statCard, { backgroundColor: '#FFF3E0' }]}>\n              <Card.Content style={styles.statContent}>\n                <MaterialIcons name=\"warning\" size={24} color=\"#FF9800\" />\n                <Text style={styles.statNumber}>{stats.warning}</Text>\n                <Text style={styles.statLabel}>تحتاج انتباه</Text>\n              </Card.Content>\n            </Card>\n\n            <Card style={[styles.statCard, { backgroundColor: '#FFEBEE' }]}>\n              <Card.Content style={styles.statContent}>\n                <MaterialIcons name=\"error\" size={24} color=\"#F44336\" />\n                <Text style={styles.statNumber}>{stats.expired}</Text>\n                <Text style={styles.statLabel}>منتهية الصلاحية</Text>\n              </Card.Content>\n            </Card>\n          </View>\n        </View>\n\n        {/* المنتجات الحديثة */}\n        <View style={styles.recentContainer}>\n          <Text style={styles.sectionTitle}>المنتجات الحديثة</Text>\n          {mockProducts.map((product) => (\n            <Card key={product.id} style={styles.productCard}>\n              <Card.Content>\n                <View style={styles.productHeader}>\n                  <View style={styles.productInfo}>\n                    <Text style={styles.productName}>{product.name}</Text>\n                    <View style={styles.productMeta}>\n                      <MaterialIcons \n                        name={getLocationIcon(product.location)} \n                        size={16} \n                        color={theme.colors.onSurfaceVariant} \n                      />\n                      <Text style={styles.productLocation}>\n                        {product.location === 'fridge' ? 'الثلاجة' : \n                         product.location === 'counter' ? 'المنضدة' : 'أخرى'}\n                      </Text>\n                    </View>\n                  </View>\n                  <Chip \n                    style={[styles.statusChip, { backgroundColor: getStatusColor(product.status) + '20' }]}\n                    textStyle={{ color: getStatusColor(product.status) }}\n                  >\n                    {product.status === 'fresh' ? 'طازج' :\n                     product.status === 'warning' ? 'تحذير' : 'منتهي'}\n                  </Chip>\n                </View>\n                <Text style={styles.expiryDate}>\n                  انتهاء الصلاحية: {product.expiryDate.toLocaleDateString('ar-SA')}\n                </Text>\n              </Card.Content>\n            </Card>\n          ))}\n        </View>\n\n        {/* أزرار سريعة */}\n        <View style={styles.quickActions}>\n          <Button \n            mode=\"contained\" \n            icon=\"qr-code-scanner\"\n            style={styles.actionButton}\n            onPress={() => {/* navigation.navigate('BarcodeScanner') */}}\n          >\n            مسح باركود\n          </Button>\n          <Button \n            mode=\"outlined\" \n            icon=\"add\"\n            style={styles.actionButton}\n            onPress={() => {/* navigation.navigate('AddProduct') */}}\n          >\n            إضافة منتج\n          </Button>\n        </View>\n      </ScrollView>\n\n      {/* زر الإضافة العائم */}\n      <FAB\n        style={styles.fab}\n        icon=\"add\"\n        onPress={() => {/* navigation.navigate('AddProduct') */}}\n      />\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  scrollView: {\n    flex: 1,\n  },\n  welcomeCard: {\n    margin: 16,\n    padding: 20,\n    borderRadius: 12,\n    elevation: 2,\n  },\n  welcomeTitle: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    textAlign: 'center',\n    marginBottom: 8,\n  },\n  welcomeSubtitle: {\n    textAlign: 'center',\n    opacity: 0.7,\n  },\n  statsContainer: {\n    margin: 16,\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 12,\n  },\n  statsRow: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n  },\n  statCard: {\n    flex: 1,\n    marginHorizontal: 4,\n    borderRadius: 12,\n  },\n  statContent: {\n    alignItems: 'center',\n    paddingVertical: 16,\n  },\n  statNumber: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginTop: 8,\n  },\n  statLabel: {\n    fontSize: 12,\n    textAlign: 'center',\n    marginTop: 4,\n  },\n  recentContainer: {\n    margin: 16,\n  },\n  productCard: {\n    marginBottom: 8,\n    borderRadius: 8,\n  },\n  productHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start',\n  },\n  productInfo: {\n    flex: 1,\n  },\n  productName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n  },\n  productMeta: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    marginTop: 4,\n  },\n  productLocation: {\n    marginLeft: 4,\n    fontSize: 12,\n    opacity: 0.7,\n  },\n  statusChip: {\n    marginLeft: 8,\n  },\n  expiryDate: {\n    fontSize: 12,\n    marginTop: 8,\n    opacity: 0.7,\n  },\n  quickActions: {\n    margin: 16,\n    gap: 12,\n  },\n  actionButton: {\n    marginBottom: 8,\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0,\n  },\n});\n\nexport default EnhancedHomeScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,cAAA;AAAA,OAAAC,UAAA;AAQxC,SACEC,IAAI,EACJC,IAAI,EACJC,KAAK,EACLC,SAAS,EACTC,MAAM,EACNC,GAAG,EACHC,IAAI,EACJC,OAAO,EACPC,QAAQ,QACH,oBAAoB;AAC3B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,aAAa,QAAQ,0BAA0B;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAGzD,IAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,WAAW;EACjBC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,SAAkB;EAC1BC,QAAQ,EAAE,QAAiB;EAC3BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,IAAIN,IAAI,CAAC;AACpB,CAAC,EACD;EACEJ,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,YAAY;EAClBC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,OAAgB;EACxBC,QAAQ,EAAE,SAAkB;EAC5BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,IAAIN,IAAI,CAAC;AACpB,CAAC,EACD;EACEJ,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,eAAe;EACrBC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,SAAkB;EAC1BC,QAAQ,EAAE,QAAiB;EAC3BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,IAAIN,IAAI,CAAC;AACpB,CAAC,CACF;AAED,IAAAO,eAAA,GAAkB7B,UAAU,CAAC8B,GAAG,CAAC,QAAQ,CAAC;EAAlCC,KAAK,GAAAF,eAAA,CAALE,KAAK;AAEb,IAAMC,kBAA4B,GAAG,SAA/BA,kBAA4BA,CAAA,EAAS;EACzC,IAAMC,UAAU,GAAGrB,aAAa,CAAC,CAAC;EAClC,IAAMsB,KAAK,GAAGzB,QAAQ,CAAC,CAAC;EACxB,IAAA0B,SAAA,GAAoCxC,QAAQ,CAAC,KAAK,CAAC;IAAAyC,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA5CG,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAGhC,IAAMI,eAAe,GAAG,SAAlBA,eAAeA,CAAA,EAAS;IAC5B,IAAMC,KAAK,GAAGxB,YAAY,CAACyB,MAAM;IACjC,IAAMC,KAAK,GAAG1B,YAAY,CAAC2B,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACrB,MAAM,KAAK,OAAO;IAAA,EAAC,CAACkB,MAAM;IACnE,IAAMI,OAAO,GAAG7B,YAAY,CAAC2B,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACrB,MAAM,KAAK,SAAS;IAAA,EAAC,CAACkB,MAAM;IACvE,IAAMK,OAAO,GAAG9B,YAAY,CAAC2B,MAAM,CAAC,UAAAC,CAAC;MAAA,OAAIA,CAAC,CAACrB,MAAM,KAAK,SAAS;IAAA,EAAC,CAACkB,MAAM;IAEvE,OAAO;MAAED,KAAK,EAALA,KAAK;MAAEE,KAAK,EAALA,KAAK;MAAEG,OAAO,EAAPA,OAAO;MAAEC,OAAO,EAAPA;IAAQ,CAAC;EAC3C,CAAC;EAED,IAAMC,KAAK,GAAGR,eAAe,CAAC,CAAC;EAE/B,IAAMS,SAAS;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;MAC5BZ,aAAa,CAAC,IAAI,CAAC;MAEnB,MAAM,IAAIa,OAAO,CAAC,UAAAC,OAAO;QAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;MAAA,EAAC;MACvDd,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAAA,gBALKU,SAASA,CAAA;MAAA,OAAAC,IAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA,GAKd;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIjC,MAAc,EAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAOU,KAAK,CAACwB,MAAM,CAACC,OAAO;IACtC;EACF,CAAC;EAED,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAInC,QAAgB,EAAK;IAC5C,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,SAAS;QAAE,OAAO,aAAa;MACpC;QAAS,OAAO,WAAW;IAC7B;EACF,CAAC;EAED,OACET,KAAA,CAACN,YAAY;IAACmD,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GACpChD,KAAA,CAAClB,UAAU;MACT+D,KAAK,EAAEC,MAAM,CAACG,UAAW;MACzBC,cAAc,EACZpD,IAAA,CAACf,cAAc;QAACuC,UAAU,EAAEA,UAAW;QAACW,SAAS,EAAEA;MAAU,CAAE,CAChE;MAAAe,QAAA,GAGDhD,KAAA,CAACR,OAAO;QAACqD,KAAK,EAAEC,MAAM,CAACK,WAAY;QAAAH,QAAA,GACjClD,IAAA,CAACX,KAAK;UAAC0D,KAAK,EAAEC,MAAM,CAACM,YAAa;UAAAJ,QAAA,EAAC;QAAoB,CAAO,CAAC,EAC/DlD,IAAA,CAACV,SAAS;UAACyD,KAAK,EAAEC,MAAM,CAACO,eAAgB;UAAAL,QAAA,EAAC;QAE1C,CAAW,CAAC;MAAA,CACL,CAAC,EAGVhD,KAAA,CAACpB,IAAI;QAACiE,KAAK,EAAEC,MAAM,CAACQ,cAAe;QAAAN,QAAA,GACjClD,IAAA,CAACb,IAAI;UAAC4D,KAAK,EAAEC,MAAM,CAACS,YAAa;UAAAP,QAAA,EAAC;QAAU,CAAM,CAAC,EACnDhD,KAAA,CAACpB,IAAI;UAACiE,KAAK,EAAEC,MAAM,CAACU,QAAS;UAAAR,QAAA,GAC3BlD,IAAA,CAACZ,IAAI;YAAC2D,KAAK,EAAE,CAACC,MAAM,CAACW,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAV,QAAA,EAC7DhD,KAAA,CAACd,IAAI,CAACyE,OAAO;cAACd,KAAK,EAAEC,MAAM,CAACc,WAAY;cAAAZ,QAAA,GACtClD,IAAA,CAACH,aAAa;gBAACQ,IAAI,EAAC,WAAW;gBAAC0D,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS,CAAE,CAAC,EAC5DhE,IAAA,CAACb,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACiB,UAAW;gBAAAf,QAAA,EAAEhB,KAAK,CAACP;cAAK,CAAO,CAAC,EACpD3B,IAAA,CAACb,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACkB,SAAU;gBAAAhB,QAAA,EAAC;cAAe,CAAM,CAAC;YAAA,CACzC;UAAC,CACX,CAAC,EAEPlD,IAAA,CAACZ,IAAI;YAAC2D,KAAK,EAAE,CAACC,MAAM,CAACW,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAV,QAAA,EAC7DhD,KAAA,CAACd,IAAI,CAACyE,OAAO;cAACd,KAAK,EAAEC,MAAM,CAACc,WAAY;cAAAZ,QAAA,GACtClD,IAAA,CAACH,aAAa;gBAACQ,IAAI,EAAC,SAAS;gBAAC0D,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS,CAAE,CAAC,EAC1DhE,IAAA,CAACb,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACiB,UAAW;gBAAAf,QAAA,EAAEhB,KAAK,CAACF;cAAO,CAAO,CAAC,EACtDhC,IAAA,CAACb,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACkB,SAAU;gBAAAhB,QAAA,EAAC;cAAY,CAAM,CAAC;YAAA,CACtC;UAAC,CACX,CAAC,EAEPlD,IAAA,CAACZ,IAAI;YAAC2D,KAAK,EAAE,CAACC,MAAM,CAACW,QAAQ,EAAE;cAAEC,eAAe,EAAE;YAAU,CAAC,CAAE;YAAAV,QAAA,EAC7DhD,KAAA,CAACd,IAAI,CAACyE,OAAO;cAACd,KAAK,EAAEC,MAAM,CAACc,WAAY;cAAAZ,QAAA,GACtClD,IAAA,CAACH,aAAa;gBAACQ,IAAI,EAAC,OAAO;gBAAC0D,IAAI,EAAE,EAAG;gBAACC,KAAK,EAAC;cAAS,CAAE,CAAC,EACxDhE,IAAA,CAACb,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACiB,UAAW;gBAAAf,QAAA,EAAEhB,KAAK,CAACD;cAAO,CAAO,CAAC,EACtDjC,IAAA,CAACb,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACkB,SAAU;gBAAAhB,QAAA,EAAC;cAAe,CAAM,CAAC;YAAA,CACzC;UAAC,CACX,CAAC;QAAA,CACH,CAAC;MAAA,CACH,CAAC,EAGPhD,KAAA,CAACpB,IAAI;QAACiE,KAAK,EAAEC,MAAM,CAACmB,eAAgB;QAAAjB,QAAA,GAClClD,IAAA,CAACb,IAAI;UAAC4D,KAAK,EAAEC,MAAM,CAACS,YAAa;UAAAP,QAAA,EAAC;QAAgB,CAAM,CAAC,EACxD/C,YAAY,CAACiE,GAAG,CAAC,UAACC,OAAO;UAAA,OACxBrE,IAAA,CAACZ,IAAI;YAAkB2D,KAAK,EAAEC,MAAM,CAACsB,WAAY;YAAApB,QAAA,EAC/ChD,KAAA,CAACd,IAAI,CAACyE,OAAO;cAAAX,QAAA,GACXhD,KAAA,CAACpB,IAAI;gBAACiE,KAAK,EAAEC,MAAM,CAACuB,aAAc;gBAAArB,QAAA,GAChChD,KAAA,CAACpB,IAAI;kBAACiE,KAAK,EAAEC,MAAM,CAACwB,WAAY;kBAAAtB,QAAA,GAC9BlD,IAAA,CAACb,IAAI;oBAAC4D,KAAK,EAAEC,MAAM,CAACyB,WAAY;oBAAAvB,QAAA,EAAEmB,OAAO,CAAChE;kBAAI,CAAO,CAAC,EACtDH,KAAA,CAACpB,IAAI;oBAACiE,KAAK,EAAEC,MAAM,CAAC0B,WAAY;oBAAAxB,QAAA,GAC9BlD,IAAA,CAACH,aAAa;sBACZQ,IAAI,EAAEyC,eAAe,CAACuB,OAAO,CAAC1D,QAAQ,CAAE;sBACxCoD,IAAI,EAAE,EAAG;sBACTC,KAAK,EAAE5C,KAAK,CAACwB,MAAM,CAAC+B;oBAAiB,CACtC,CAAC,EACF3E,IAAA,CAACb,IAAI;sBAAC4D,KAAK,EAAEC,MAAM,CAAC4B,eAAgB;sBAAA1B,QAAA,EACjCmB,OAAO,CAAC1D,QAAQ,KAAK,QAAQ,GAAG,SAAS,GACzC0D,OAAO,CAAC1D,QAAQ,KAAK,SAAS,GAAG,SAAS,GAAG;oBAAM,CAChD,CAAC;kBAAA,CACH,CAAC;gBAAA,CACH,CAAC,EACPX,IAAA,CAACP,IAAI;kBACHsD,KAAK,EAAE,CAACC,MAAM,CAAC6B,UAAU,EAAE;oBAAEjB,eAAe,EAAEjB,cAAc,CAAC0B,OAAO,CAAC3D,MAAM,CAAC,GAAG;kBAAK,CAAC,CAAE;kBACvFoE,SAAS,EAAE;oBAAEd,KAAK,EAAErB,cAAc,CAAC0B,OAAO,CAAC3D,MAAM;kBAAE,CAAE;kBAAAwC,QAAA,EAEpDmB,OAAO,CAAC3D,MAAM,KAAK,OAAO,GAAG,MAAM,GACnC2D,OAAO,CAAC3D,MAAM,KAAK,SAAS,GAAG,OAAO,GAAG;gBAAO,CAC7C,CAAC;cAAA,CACH,CAAC,EACPR,KAAA,CAACf,IAAI;gBAAC4D,KAAK,EAAEC,MAAM,CAACzC,UAAW;gBAAA2C,QAAA,GAAC,yFACb,EAACmB,OAAO,CAAC9D,UAAU,CAACwE,kBAAkB,CAAC,OAAO,CAAC;cAAA,CAC5D,CAAC;YAAA,CACK;UAAC,GA5BNV,OAAO,CAACjE,EA6Bb,CAAC;QAAA,CACR,CAAC;MAAA,CACE,CAAC,EAGPF,KAAA,CAACpB,IAAI;QAACiE,KAAK,EAAEC,MAAM,CAACgC,YAAa;QAAA9B,QAAA,GAC/BlD,IAAA,CAACT,MAAM;UACL0F,IAAI,EAAC,WAAW;UAChBC,IAAI,EAAC,iBAAiB;UACtBnC,KAAK,EAAEC,MAAM,CAACmC,YAAa;UAC3BC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ,CAA4C,CAAE;UAAAlC,QAAA,EAC9D;QAED,CAAQ,CAAC,EACTlD,IAAA,CAACT,MAAM;UACL0F,IAAI,EAAC,UAAU;UACfC,IAAI,EAAC,KAAK;UACVnC,KAAK,EAAEC,MAAM,CAACmC,YAAa;UAC3BC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ,CAAwC,CAAE;UAAAlC,QAAA,EAC1D;QAED,CAAQ,CAAC;MAAA,CACL,CAAC;IAAA,CACG,CAAC,EAGblD,IAAA,CAACR,GAAG;MACFuD,KAAK,EAAEC,MAAM,CAACqC,GAAI;MAClBH,IAAI,EAAC,KAAK;MACVE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ,CAAwC;IAAE,CAC1D,CAAC;EAAA,CACU,CAAC;AAEnB,CAAC;AAED,IAAMpC,MAAM,GAAGjE,UAAU,CAACuG,MAAM,CAAC;EAC/BrC,SAAS,EAAE;IACTsC,IAAI,EAAE,CAAC;IACP3B,eAAe,EAAE;EACnB,CAAC;EACDT,UAAU,EAAE;IACVoC,IAAI,EAAE;EACR,CAAC;EACDlC,WAAW,EAAE;IACXmC,MAAM,EAAE,EAAE;IACVC,OAAO,EAAE,EAAE;IACXC,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACDrC,YAAY,EAAE;IACZsC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE,QAAQ;IACnBC,YAAY,EAAE;EAChB,CAAC;EACDxC,eAAe,EAAE;IACfuC,SAAS,EAAE,QAAQ;IACnBE,OAAO,EAAE;EACX,CAAC;EACDxC,cAAc,EAAE;IACdgC,MAAM,EAAE;EACV,CAAC;EACD/B,YAAY,EAAE;IACZmC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBE,YAAY,EAAE;EAChB,CAAC;EACDrC,QAAQ,EAAE;IACRuC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE;EAClB,CAAC;EACDvC,QAAQ,EAAE;IACR4B,IAAI,EAAE,CAAC;IACPY,gBAAgB,EAAE,CAAC;IACnBT,YAAY,EAAE;EAChB,CAAC;EACD5B,WAAW,EAAE;IACXsC,UAAU,EAAE,QAAQ;IACpBC,eAAe,EAAE;EACnB,CAAC;EACDpC,UAAU,EAAE;IACV2B,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBS,SAAS,EAAE;EACb,CAAC;EACDpC,SAAS,EAAE;IACT0B,QAAQ,EAAE,EAAE;IACZE,SAAS,EAAE,QAAQ;IACnBQ,SAAS,EAAE;EACb,CAAC;EACDnC,eAAe,EAAE;IACfqB,MAAM,EAAE;EACV,CAAC;EACDlB,WAAW,EAAE;IACXyB,YAAY,EAAE,CAAC;IACfL,YAAY,EAAE;EAChB,CAAC;EACDnB,aAAa,EAAE;IACb0B,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BE,UAAU,EAAE;EACd,CAAC;EACD5B,WAAW,EAAE;IACXe,IAAI,EAAE;EACR,CAAC;EACDd,WAAW,EAAE;IACXmB,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDnB,WAAW,EAAE;IACXuB,aAAa,EAAE,KAAK;IACpBG,UAAU,EAAE,QAAQ;IACpBE,SAAS,EAAE;EACb,CAAC;EACD1B,eAAe,EAAE;IACf2B,UAAU,EAAE,CAAC;IACbX,QAAQ,EAAE,EAAE;IACZI,OAAO,EAAE;EACX,CAAC;EACDnB,UAAU,EAAE;IACV0B,UAAU,EAAE;EACd,CAAC;EACDhG,UAAU,EAAE;IACVqF,QAAQ,EAAE,EAAE;IACZU,SAAS,EAAE,CAAC;IACZN,OAAO,EAAE;EACX,CAAC;EACDhB,YAAY,EAAE;IACZQ,MAAM,EAAE,EAAE;IACVgB,GAAG,EAAE;EACP,CAAC;EACDrB,YAAY,EAAE;IACZY,YAAY,EAAE;EAChB,CAAC;EACDV,GAAG,EAAE;IACHoB,QAAQ,EAAE,UAAU;IACpBjB,MAAM,EAAE,EAAE;IACVkB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAezF,kBAAkB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}