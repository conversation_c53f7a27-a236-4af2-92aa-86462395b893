{"ast": null, "code": "import * as React from 'react';\nfunction StaticContainer(props) {\n  return props.children;\n}\nexport default React.memo(StaticContainer, function (prevProps, nextProps) {\n  var prevPropKeys = Object.keys(prevProps);\n  var nextPropKeys = Object.keys(nextProps);\n  if (prevPropKeys.length !== nextPropKeys.length) {\n    return false;\n  }\n  for (var key of prevPropKeys) {\n    if (key === 'children') {\n      continue;\n    }\n    if (prevProps[key] !== nextProps[key]) {\n      return false;\n    }\n  }\n  return true;\n});", "map": {"version": 3, "names": ["React", "StaticContainer", "props", "children", "memo", "prevProps", "nextProps", "prevPropKeys", "Object", "keys", "nextPropKeys", "length", "key"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\@react-navigation\\core\\src\\StaticContainer.tsx"], "sourcesContent": ["import * as React from 'react';\n\n/**\n * Component which prevents updates for children if no props changed\n */\nfunction StaticContainer(props: any) {\n  return props.children;\n}\n\nexport default React.memo(StaticContainer, (prevProps: any, nextProps: any) => {\n  const prevPropKeys = Object.keys(prevProps);\n  const nextPropKeys = Object.keys(nextProps);\n\n  if (prevPropKeys.length !== nextPropKeys.length) {\n    return false;\n  }\n\n  for (const key of prevPropKeys) {\n    if (key === 'children') {\n      continue;\n    }\n\n    if (prevProps[key] !== nextProps[key]) {\n      return false;\n    }\n  }\n\n  return true;\n});\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAK9B,SAASC,eAAeA,CAACC,KAAU,EAAE;EACnC,OAAOA,KAAK,CAACC,QAAQ;AACvB;AAEA,eAAeH,KAAK,CAACI,IAAI,CAACH,eAAe,EAAE,UAACI,SAAc,EAAEC,SAAc,EAAK;EAC7E,IAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACJ,SAAS,CAAC;EAC3C,IAAMK,YAAY,GAAGF,MAAM,CAACC,IAAI,CAACH,SAAS,CAAC;EAE3C,IAAIC,YAAY,CAACI,MAAM,KAAKD,YAAY,CAACC,MAAM,EAAE;IAC/C,OAAO,KAAK;EACd;EAEA,KAAK,IAAMC,GAAG,IAAIL,YAAY,EAAE;IAC9B,IAAIK,GAAG,KAAK,UAAU,EAAE;MACtB;IACF;IAEA,IAAIP,SAAS,CAACO,GAAG,CAAC,KAAKN,SAAS,CAACM,GAAG,CAAC,EAAE;MACrC,OAAO,KAAK;IACd;EACF;EAEA,OAAO,IAAI;AACb,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}