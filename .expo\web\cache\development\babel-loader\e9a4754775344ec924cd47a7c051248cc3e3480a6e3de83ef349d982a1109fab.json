{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport Alert from \"react-native-web/dist/exports/Alert\";\nimport { Text, TextInput, Button, Card, SegmentedButtons, useTheme, Snackbar } from 'react-native-paper';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport DateTimePicker from '@react-native-community/datetimepicker';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar EnhancedAddProductScreen = function EnhancedAddProductScreen() {\n  var theme = useTheme();\n  var _useState = useState(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    productName = _useState2[0],\n    setProductName = _useState2[1];\n  var _useState3 = useState(''),\n    _useState4 = _slicedToArray(_useState3, 2),\n    barcode = _useState4[0],\n    setBarcode = _useState4[1];\n  var _useState5 = useState('1'),\n    _useState6 = _slicedToArray(_useState5, 2),\n    quantity = _useState6[0],\n    setQuantity = _useState6[1];\n  var _useState7 = useState('fridge'),\n    _useState8 = _slicedToArray(_useState7, 2),\n    selectedLocation = _useState8[0],\n    setSelectedLocation = _useState8[1];\n  var _useState9 = useState(new Date()),\n    _useState0 = _slicedToArray(_useState9, 2),\n    expiryDate = _useState0[0],\n    setExpiryDate = _useState0[1];\n  var _useState1 = useState(false),\n    _useState10 = _slicedToArray(_useState1, 2),\n    showDatePicker = _useState10[0],\n    setShowDatePicker = _useState10[1];\n  var _useState11 = useState(''),\n    _useState12 = _slicedToArray(_useState11, 2),\n    brand = _useState12[0],\n    setBrand = _useState12[1];\n  var _useState13 = useState(''),\n    _useState14 = _slicedToArray(_useState13, 2),\n    notes = _useState14[0],\n    setNotes = _useState14[1];\n  var _useState15 = useState(false),\n    _useState16 = _slicedToArray(_useState15, 2),\n    isLoading = _useState16[0],\n    setIsLoading = _useState16[1];\n  var _useState17 = useState(false),\n    _useState18 = _slicedToArray(_useState17, 2),\n    snackbarVisible = _useState18[0],\n    setSnackbarVisible = _useState18[1];\n  var _useState19 = useState(''),\n    _useState20 = _slicedToArray(_useState19, 2),\n    snackbarMessage = _useState20[0],\n    setSnackbarMessage = _useState20[1];\n  var locationOptions = [{\n    value: 'fridge',\n    label: '🧊 ثلاجة'\n  }, {\n    value: 'freezer',\n    label: '❄️ فريزر'\n  }, {\n    value: 'pantry',\n    label: '🏪 مخزن'\n  }, {\n    value: 'counter',\n    label: '🏠 منضدة'\n  }];\n  var handleDateChange = function handleDateChange(event, selectedDate) {\n    setShowDatePicker(false);\n    if (selectedDate) {\n      setExpiryDate(selectedDate);\n    }\n  };\n  var handleScanBarcode = function handleScanBarcode() {\n    Alert.alert('مسح الباركود', 'سيتم فتح الكاميرا لمسح الباركود', [{\n      text: 'إلغاء',\n      style: 'cancel'\n    }, {\n      text: 'موافق',\n      onPress: function onPress() {\n        setBarcode('123456789012');\n        setProductName('منتج ممسوح');\n        setBrand('علامة تجارية');\n        showSnackbar('تم مسح الباركود بنجاح');\n      }\n    }]);\n  };\n  var handleSaveProduct = function () {\n    var _ref = _asyncToGenerator(function* () {\n      if (!productName.trim()) {\n        showSnackbar('يرجى إدخال اسم المنتج');\n        return;\n      }\n      if (!quantity || parseInt(quantity) <= 0) {\n        showSnackbar('يرجى إدخال كمية صحيحة');\n        return;\n      }\n      setIsLoading(true);\n      try {\n        yield new Promise(function (resolve) {\n          return setTimeout(resolve, 1500);\n        });\n        var newProduct = {\n          id: Date.now().toString(),\n          name: productName.trim(),\n          barcode: barcode.trim(),\n          quantity: parseInt(quantity),\n          location: selectedLocation,\n          expiryDate: expiryDate,\n          brand: brand.trim(),\n          notes: notes.trim(),\n          addedAt: new Date(),\n          status: calculateStatus(expiryDate)\n        };\n        console.log('منتج جديد:', newProduct);\n        resetForm();\n        showSnackbar('تم إضافة المنتج بنجاح');\n      } catch (error) {\n        showSnackbar('حدث خطأ أثناء إضافة المنتج');\n      } finally {\n        setIsLoading(false);\n      }\n    });\n    return function handleSaveProduct() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  var calculateStatus = function calculateStatus(expiry) {\n    var today = new Date();\n    var diffTime = expiry.getTime() - today.getTime();\n    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    if (diffDays < 0) return 'expired';\n    if (diffDays <= 3) return 'warning';\n    return 'fresh';\n  };\n  var resetForm = function resetForm() {\n    setProductName('');\n    setBarcode('');\n    setQuantity('1');\n    setSelectedLocation('fridge');\n    setExpiryDate(new Date());\n    setBrand('');\n    setNotes('');\n  };\n  var showSnackbar = function showSnackbar(message) {\n    setSnackbarMessage(message);\n    setSnackbarVisible(true);\n  };\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    children: [_jsxs(ScrollView, {\n      style: styles.scrollView,\n      children: [_jsx(Card, {\n        style: styles.card,\n        children: _jsxs(Card.Content, {\n          children: [_jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"\\u0645\\u0639\\u0644\\u0648\\u0645\\u0627\\u062A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\"\n          }), _jsx(TextInput, {\n            label: \"\\u0627\\u0633\\u0645 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C *\",\n            value: productName,\n            onChangeText: setProductName,\n            style: styles.input,\n            mode: \"outlined\",\n            placeholder: \"\\u0645\\u062B\\u0627\\u0644: \\u062D\\u0644\\u064A\\u0628 \\u0646\\u0627\\u062F\\u0643 \\u0643\\u0627\\u0645\\u0644 \\u0627\\u0644\\u062F\\u0633\\u0645\"\n          }), _jsx(TextInput, {\n            label: \"\\u0627\\u0644\\u0639\\u0644\\u0627\\u0645\\u0629 \\u0627\\u0644\\u062A\\u062C\\u0627\\u0631\\u064A\\u0629\",\n            value: brand,\n            onChangeText: setBrand,\n            style: styles.input,\n            mode: \"outlined\",\n            placeholder: \"\\u0645\\u062B\\u0627\\u0644: \\u0646\\u0627\\u062F\\u0643\"\n          }), _jsxs(View, {\n            style: styles.barcodeContainer,\n            children: [_jsx(TextInput, {\n              label: \"\\u0627\\u0644\\u0628\\u0627\\u0631\\u0643\\u0648\\u062F\",\n              value: barcode,\n              onChangeText: setBarcode,\n              style: [styles.input, styles.barcodeInput],\n              mode: \"outlined\",\n              placeholder: \"123456789012\",\n              keyboardType: \"numeric\"\n            }), _jsx(Button, {\n              mode: \"contained\",\n              onPress: handleScanBarcode,\n              style: styles.scanButton,\n              icon: \"qr-code-scanner\",\n              children: \"\\u0645\\u0633\\u062D\"\n            })]\n          }), _jsx(TextInput, {\n            label: \"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629 *\",\n            value: quantity,\n            onChangeText: setQuantity,\n            style: styles.input,\n            mode: \"outlined\",\n            keyboardType: \"numeric\",\n            placeholder: \"1\"\n          })]\n        })\n      }), _jsx(Card, {\n        style: styles.card,\n        children: _jsxs(Card.Content, {\n          children: [_jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"\\u0645\\u0648\\u0642\\u0639 \\u0627\\u0644\\u062A\\u062E\\u0632\\u064A\\u0646\"\n          }), _jsx(SegmentedButtons, {\n            value: selectedLocation,\n            onValueChange: setSelectedLocation,\n            buttons: locationOptions,\n            style: styles.locationButtons\n          })]\n        })\n      }), _jsx(Card, {\n        style: styles.card,\n        children: _jsxs(Card.Content, {\n          children: [_jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"\\u062A\\u0627\\u0631\\u064A\\u062E \\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0635\\u0644\\u0627\\u062D\\u064A\\u0629\"\n          }), _jsx(Button, {\n            mode: \"outlined\",\n            onPress: function onPress() {\n              return setShowDatePicker(true);\n            },\n            style: styles.dateButton,\n            icon: \"calendar\",\n            children: expiryDate.toLocaleDateString('ar-SA')\n          }), showDatePicker && _jsx(DateTimePicker, {\n            value: expiryDate,\n            mode: \"date\",\n            display: \"default\",\n            onChange: handleDateChange,\n            minimumDate: new Date()\n          }), _jsxs(View, {\n            style: styles.statusPreview,\n            children: [_jsx(Text, {\n              style: styles.statusLabel,\n              children: \"\\u062D\\u0627\\u0644\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C \\u0627\\u0644\\u0645\\u062A\\u0648\\u0642\\u0639\\u0629:\"\n            }), _jsx(View, {\n              style: [styles.statusIndicator, {\n                backgroundColor: getStatusColor(calculateStatus(expiryDate))\n              }],\n              children: _jsx(Text, {\n                style: styles.statusText,\n                children: getStatusName(calculateStatus(expiryDate))\n              })\n            })]\n          })]\n        })\n      }), _jsx(Card, {\n        style: styles.card,\n        children: _jsxs(Card.Content, {\n          children: [_jsx(Text, {\n            style: styles.sectionTitle,\n            children: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629\"\n          }), _jsx(TextInput, {\n            label: \"\\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A\",\n            value: notes,\n            onChangeText: setNotes,\n            style: styles.input,\n            mode: \"outlined\",\n            multiline: true,\n            numberOfLines: 3,\n            placeholder: \"\\u0623\\u064A \\u0645\\u0644\\u0627\\u062D\\u0638\\u0627\\u062A \\u0625\\u0636\\u0627\\u0641\\u064A\\u0629 \\u062D\\u0648\\u0644 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C...\"\n          })]\n        })\n      }), _jsxs(View, {\n        style: styles.buttonContainer,\n        children: [_jsx(Button, {\n          mode: \"contained\",\n          onPress: handleSaveProduct,\n          loading: isLoading,\n          disabled: isLoading,\n          style: styles.saveButton,\n          icon: \"check\",\n          children: isLoading ? 'جاري الحفظ...' : 'حفظ المنتج'\n        }), _jsx(Button, {\n          mode: \"outlined\",\n          onPress: resetForm,\n          disabled: isLoading,\n          style: styles.resetButton,\n          icon: \"refresh\",\n          children: \"\\u0625\\u0639\\u0627\\u062F\\u0629 \\u062A\\u0639\\u064A\\u064A\\u0646\"\n        })]\n      })]\n    }), _jsx(Snackbar, {\n      visible: snackbarVisible,\n      onDismiss: function onDismiss() {\n        return setSnackbarVisible(false);\n      },\n      duration: 3000,\n      children: snackbarMessage\n    })]\n  });\n};\nvar getStatusColor = function getStatusColor(status) {\n  switch (status) {\n    case 'fresh':\n      return '#4CAF50';\n    case 'warning':\n      return '#FF9800';\n    case 'expired':\n      return '#F44336';\n    default:\n      return '#2196F3';\n  }\n};\nvar getStatusName = function getStatusName(status) {\n  switch (status) {\n    case 'fresh':\n      return 'طازج';\n    case 'warning':\n      return 'يحتاج انتباه';\n    case 'expired':\n      return 'منتهي الصلاحية';\n    default:\n      return status;\n  }\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  scrollView: {\n    flex: 1\n  },\n  card: {\n    margin: 16,\n    marginBottom: 8,\n    borderRadius: 12\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 16\n  },\n  input: {\n    marginBottom: 12\n  },\n  barcodeContainer: {\n    flexDirection: 'row',\n    alignItems: 'flex-end',\n    gap: 8\n  },\n  barcodeInput: {\n    flex: 1\n  },\n  scanButton: {\n    marginBottom: 12\n  },\n  locationButtons: {\n    marginBottom: 8\n  },\n  dateButton: {\n    marginBottom: 16\n  },\n  statusPreview: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between'\n  },\n  statusLabel: {\n    fontSize: 14,\n    opacity: 0.7\n  },\n  statusIndicator: {\n    paddingHorizontal: 12,\n    paddingVertical: 6,\n    borderRadius: 16\n  },\n  statusText: {\n    color: 'white',\n    fontSize: 12,\n    fontWeight: 'bold'\n  },\n  buttonContainer: {\n    margin: 16,\n    gap: 12\n  },\n  saveButton: {\n    paddingVertical: 8\n  },\n  resetButton: {\n    paddingVertical: 8\n  }\n});\nexport default EnhancedAddProductScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "StyleSheet", "ScrollView", "<PERSON><PERSON>", "Text", "TextInput", "<PERSON><PERSON>", "Card", "SegmentedButtons", "useTheme", "Snackbar", "SafeAreaView", "DateTimePicker", "jsx", "_jsx", "jsxs", "_jsxs", "EnhancedAddProductScreen", "theme", "_useState", "_useState2", "_slicedToArray", "productName", "setProductName", "_useState3", "_useState4", "barcode", "setBarcode", "_useState5", "_useState6", "quantity", "setQuantity", "_useState7", "_useState8", "selectedLocation", "setSelectedLocation", "_useState9", "Date", "_useState0", "expiryDate", "setExpiryDate", "_useState1", "_useState10", "showDatePicker", "setShowDatePicker", "_useState11", "_useState12", "brand", "<PERSON><PERSON><PERSON>", "_useState13", "_useState14", "notes", "setNotes", "_useState15", "_useState16", "isLoading", "setIsLoading", "_useState17", "_useState18", "snackbarVisible", "setSnackbarVisible", "_useState19", "_useState20", "snackbarMessage", "setSnackbarMessage", "locationOptions", "value", "label", "handleDateChange", "event", "selectedDate", "handleScanBarcode", "alert", "text", "style", "onPress", "showSnackbar", "handleSaveProduct", "_ref", "_asyncToGenerator", "trim", "parseInt", "Promise", "resolve", "setTimeout", "newProduct", "id", "now", "toString", "name", "location", "addedAt", "status", "calculateStatus", "console", "log", "resetForm", "error", "apply", "arguments", "expiry", "today", "diffTime", "getTime", "diffDays", "Math", "ceil", "message", "styles", "container", "children", "scrollView", "card", "Content", "sectionTitle", "onChangeText", "input", "mode", "placeholder", "barcodeContainer", "barcodeInput", "keyboardType", "scanButton", "icon", "onValueChange", "buttons", "locationButtons", "dateButton", "toLocaleDateString", "display", "onChange", "minimumDate", "statusPreview", "statusLabel", "statusIndicator", "backgroundColor", "getStatusColor", "statusText", "getStatusName", "multiline", "numberOfLines", "buttonContainer", "loading", "disabled", "saveButton", "resetButton", "visible", "on<PERSON><PERSON><PERSON>", "duration", "create", "flex", "margin", "marginBottom", "borderRadius", "fontSize", "fontWeight", "flexDirection", "alignItems", "gap", "justifyContent", "opacity", "paddingHorizontal", "paddingVertical", "color"], "sources": ["C:/Users/<USER>/Music/mobile/src/screens/EnhancedAddProductScreen.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  View,\n  StyleSheet,\n  ScrollView,\n  Alert,\n} from 'react-native';\nimport {\n  Text,\n  TextInput,\n  Button,\n  Card,\n  SegmentedButtons,\n  useTheme,\n  Snackbar,\n} from 'react-native-paper';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport DateTimePicker from '@react-native-community/datetimepicker';\n\nconst EnhancedAddProductScreen: React.FC = () => {\n  const theme = useTheme();\n  const [productName, setProductName] = useState('');\n  const [barcode, setBarcode] = useState('');\n  const [quantity, setQuantity] = useState('1');\n  const [selectedLocation, setSelectedLocation] = useState('fridge');\n  const [expiryDate, setExpiryDate] = useState(new Date());\n  const [showDatePicker, setShowDatePicker] = useState(false);\n  const [brand, setBrand] = useState('');\n  const [notes, setNotes] = useState('');\n  const [isLoading, setIsLoading] = useState(false);\n  const [snackbarVisible, setSnackbarVisible] = useState(false);\n  const [snackbarMessage, setSnackbarMessage] = useState('');\n\n  const locationOptions = [\n    { value: 'fridge', label: '🧊 ثلاجة' },\n    { value: 'freezer', label: '❄️ فريزر' },\n    { value: 'pantry', label: '🏪 مخزن' },\n    { value: 'counter', label: '🏠 منضدة' },\n  ];\n\n  const handleDateChange = (event: any, selectedDate?: Date) => {\n    setShowDatePicker(false);\n    if (selectedDate) {\n      setExpiryDate(selectedDate);\n    }\n  };\n\n  const handleScanBarcode = () => {\n    // محاكاة مسح الباركود\n    Alert.alert(\n      'مسح الباركود',\n      'سيتم فتح الكاميرا لمسح الباركود',\n      [\n        { text: 'إلغاء', style: 'cancel' },\n        { \n          text: 'موافق', \n          onPress: () => {\n            // محاكاة نتيجة المسح\n            setBarcode('123456789012');\n            setProductName('منتج ممسوح');\n            setBrand('علامة تجارية');\n            showSnackbar('تم مسح الباركود بنجاح');\n          }\n        },\n      ]\n    );\n  };\n\n  const handleSaveProduct = async () => {\n    // التحقق من صحة البيانات\n    if (!productName.trim()) {\n      showSnackbar('يرجى إدخال اسم المنتج');\n      return;\n    }\n\n    if (!quantity || parseInt(quantity) <= 0) {\n      showSnackbar('يرجى إدخال كمية صحيحة');\n      return;\n    }\n\n    setIsLoading(true);\n\n    try {\n      // محاكاة حفظ المنتج\n      await new Promise(resolve => setTimeout(resolve, 1500));\n\n      const newProduct = {\n        id: Date.now().toString(),\n        name: productName.trim(),\n        barcode: barcode.trim(),\n        quantity: parseInt(quantity),\n        location: selectedLocation,\n        expiryDate,\n        brand: brand.trim(),\n        notes: notes.trim(),\n        addedAt: new Date(),\n        status: calculateStatus(expiryDate),\n      };\n\n      console.log('منتج جديد:', newProduct);\n      \n      // إعادة تعيين النموذج\n      resetForm();\n      showSnackbar('تم إضافة المنتج بنجاح');\n      \n    } catch (error) {\n      showSnackbar('حدث خطأ أثناء إضافة المنتج');\n    } finally {\n      setIsLoading(false);\n    }\n  };\n\n  const calculateStatus = (expiry: Date) => {\n    const today = new Date();\n    const diffTime = expiry.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    \n    if (diffDays < 0) return 'expired';\n    if (diffDays <= 3) return 'warning';\n    return 'fresh';\n  };\n\n  const resetForm = () => {\n    setProductName('');\n    setBarcode('');\n    setQuantity('1');\n    setSelectedLocation('fridge');\n    setExpiryDate(new Date());\n    setBrand('');\n    setNotes('');\n  };\n\n  const showSnackbar = (message: string) => {\n    setSnackbarMessage(message);\n    setSnackbarVisible(true);\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <ScrollView style={styles.scrollView}>\n        <Card style={styles.card}>\n          <Card.Content>\n            <Text style={styles.sectionTitle}>معلومات المنتج</Text>\n            \n            {/* اسم المنتج */}\n            <TextInput\n              label=\"اسم المنتج *\"\n              value={productName}\n              onChangeText={setProductName}\n              style={styles.input}\n              mode=\"outlined\"\n              placeholder=\"مثال: حليب نادك كامل الدسم\"\n            />\n\n            {/* العلامة التجارية */}\n            <TextInput\n              label=\"العلامة التجارية\"\n              value={brand}\n              onChangeText={setBrand}\n              style={styles.input}\n              mode=\"outlined\"\n              placeholder=\"مثال: نادك\"\n            />\n\n            {/* الباركود */}\n            <View style={styles.barcodeContainer}>\n              <TextInput\n                label=\"الباركود\"\n                value={barcode}\n                onChangeText={setBarcode}\n                style={[styles.input, styles.barcodeInput]}\n                mode=\"outlined\"\n                placeholder=\"123456789012\"\n                keyboardType=\"numeric\"\n              />\n              <Button\n                mode=\"contained\"\n                onPress={handleScanBarcode}\n                style={styles.scanButton}\n                icon=\"qr-code-scanner\"\n              >\n                مسح\n              </Button>\n            </View>\n\n            {/* الكمية */}\n            <TextInput\n              label=\"الكمية *\"\n              value={quantity}\n              onChangeText={setQuantity}\n              style={styles.input}\n              mode=\"outlined\"\n              keyboardType=\"numeric\"\n              placeholder=\"1\"\n            />\n          </Card.Content>\n        </Card>\n\n        <Card style={styles.card}>\n          <Card.Content>\n            <Text style={styles.sectionTitle}>موقع التخزين</Text>\n            <SegmentedButtons\n              value={selectedLocation}\n              onValueChange={setSelectedLocation}\n              buttons={locationOptions}\n              style={styles.locationButtons}\n            />\n          </Card.Content>\n        </Card>\n\n        <Card style={styles.card}>\n          <Card.Content>\n            <Text style={styles.sectionTitle}>تاريخ انتهاء الصلاحية</Text>\n            \n            <Button\n              mode=\"outlined\"\n              onPress={() => setShowDatePicker(true)}\n              style={styles.dateButton}\n              icon=\"calendar\"\n            >\n              {expiryDate.toLocaleDateString('ar-SA')}\n            </Button>\n\n            {showDatePicker && (\n              <DateTimePicker\n                value={expiryDate}\n                mode=\"date\"\n                display=\"default\"\n                onChange={handleDateChange}\n                minimumDate={new Date()}\n              />\n            )}\n\n            <View style={styles.statusPreview}>\n              <Text style={styles.statusLabel}>حالة المنتج المتوقعة:</Text>\n              <View style={[\n                styles.statusIndicator,\n                { backgroundColor: getStatusColor(calculateStatus(expiryDate)) }\n              ]}>\n                <Text style={styles.statusText}>\n                  {getStatusName(calculateStatus(expiryDate))}\n                </Text>\n              </View>\n            </View>\n          </Card.Content>\n        </Card>\n\n        <Card style={styles.card}>\n          <Card.Content>\n            <Text style={styles.sectionTitle}>ملاحظات إضافية</Text>\n            <TextInput\n              label=\"ملاحظات\"\n              value={notes}\n              onChangeText={setNotes}\n              style={styles.input}\n              mode=\"outlined\"\n              multiline\n              numberOfLines={3}\n              placeholder=\"أي ملاحظات إضافية حول المنتج...\"\n            />\n          </Card.Content>\n        </Card>\n\n        <View style={styles.buttonContainer}>\n          <Button\n            mode=\"contained\"\n            onPress={handleSaveProduct}\n            loading={isLoading}\n            disabled={isLoading}\n            style={styles.saveButton}\n            icon=\"check\"\n          >\n            {isLoading ? 'جاري الحفظ...' : 'حفظ المنتج'}\n          </Button>\n          \n          <Button\n            mode=\"outlined\"\n            onPress={resetForm}\n            disabled={isLoading}\n            style={styles.resetButton}\n            icon=\"refresh\"\n          >\n            إعادة تعيين\n          </Button>\n        </View>\n      </ScrollView>\n\n      <Snackbar\n        visible={snackbarVisible}\n        onDismiss={() => setSnackbarVisible(false)}\n        duration={3000}\n      >\n        {snackbarMessage}\n      </Snackbar>\n    </SafeAreaView>\n  );\n};\n\nconst getStatusColor = (status: string) => {\n  switch (status) {\n    case 'fresh': return '#4CAF50';\n    case 'warning': return '#FF9800';\n    case 'expired': return '#F44336';\n    default: return '#2196F3';\n  }\n};\n\nconst getStatusName = (status: string) => {\n  switch (status) {\n    case 'fresh': return 'طازج';\n    case 'warning': return 'يحتاج انتباه';\n    case 'expired': return 'منتهي الصلاحية';\n    default: return status;\n  }\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  scrollView: {\n    flex: 1,\n  },\n  card: {\n    margin: 16,\n    marginBottom: 8,\n    borderRadius: 12,\n  },\n  sectionTitle: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginBottom: 16,\n  },\n  input: {\n    marginBottom: 12,\n  },\n  barcodeContainer: {\n    flexDirection: 'row',\n    alignItems: 'flex-end',\n    gap: 8,\n  },\n  barcodeInput: {\n    flex: 1,\n  },\n  scanButton: {\n    marginBottom: 12,\n  },\n  locationButtons: {\n    marginBottom: 8,\n  },\n  dateButton: {\n    marginBottom: 16,\n  },\n  statusPreview: {\n    flexDirection: 'row',\n    alignItems: 'center',\n    justifyContent: 'space-between',\n  },\n  statusLabel: {\n    fontSize: 14,\n    opacity: 0.7,\n  },\n  statusIndicator: {\n    paddingHorizontal: 12,\n    paddingVertical: 6,\n    borderRadius: 16,\n  },\n  statusText: {\n    color: 'white',\n    fontSize: 12,\n    fontWeight: 'bold',\n  },\n  buttonContainer: {\n    margin: 16,\n    gap: 12,\n  },\n  saveButton: {\n    paddingVertical: 8,\n  },\n  resetButton: {\n    paddingVertical: 8,\n  },\n});\n\nexport default EnhancedAddProductScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,KAAA;AAOxC,SACEC,IAAI,EACJC,SAAS,EACTC,MAAM,EACNC,IAAI,EACJC,gBAAgB,EAChBC,QAAQ,EACRC,QAAQ,QACH,oBAAoB;AAC3B,SAASC,YAAY,QAAQ,gCAAgC;AAE7D,OAAOC,cAAc,MAAM,wCAAwC;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAEpE,IAAMC,wBAAkC,GAAG,SAArCA,wBAAkCA,CAAA,EAAS;EAC/C,IAAMC,KAAK,GAAGT,QAAQ,CAAC,CAAC;EACxB,IAAAU,SAAA,GAAsCpB,QAAQ,CAAC,EAAE,CAAC;IAAAqB,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA3CG,WAAW,GAAAF,UAAA;IAAEG,cAAc,GAAAH,UAAA;EAClC,IAAAI,UAAA,GAA8BzB,QAAQ,CAAC,EAAE,CAAC;IAAA0B,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAAnCE,OAAO,GAAAD,UAAA;IAAEE,UAAU,GAAAF,UAAA;EAC1B,IAAAG,UAAA,GAAgC7B,QAAQ,CAAC,GAAG,CAAC;IAAA8B,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAAtCE,QAAQ,GAAAD,UAAA;IAAEE,WAAW,GAAAF,UAAA;EAC5B,IAAAG,UAAA,GAAgDjC,QAAQ,CAAC,QAAQ,CAAC;IAAAkC,UAAA,GAAAZ,cAAA,CAAAW,UAAA;IAA3DE,gBAAgB,GAAAD,UAAA;IAAEE,mBAAmB,GAAAF,UAAA;EAC5C,IAAAG,UAAA,GAAoCrC,QAAQ,CAAC,IAAIsC,IAAI,CAAC,CAAC,CAAC;IAAAC,UAAA,GAAAjB,cAAA,CAAAe,UAAA;IAAjDG,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAChC,IAAAG,UAAA,GAA4C1C,QAAQ,CAAC,KAAK,CAAC;IAAA2C,WAAA,GAAArB,cAAA,CAAAoB,UAAA;IAApDE,cAAc,GAAAD,WAAA;IAAEE,iBAAiB,GAAAF,WAAA;EACxC,IAAAG,WAAA,GAA0B9C,QAAQ,CAAC,EAAE,CAAC;IAAA+C,WAAA,GAAAzB,cAAA,CAAAwB,WAAA;IAA/BE,KAAK,GAAAD,WAAA;IAAEE,QAAQ,GAAAF,WAAA;EACtB,IAAAG,WAAA,GAA0BlD,QAAQ,CAAC,EAAE,CAAC;IAAAmD,WAAA,GAAA7B,cAAA,CAAA4B,WAAA;IAA/BE,KAAK,GAAAD,WAAA;IAAEE,QAAQ,GAAAF,WAAA;EACtB,IAAAG,WAAA,GAAkCtD,QAAQ,CAAC,KAAK,CAAC;IAAAuD,WAAA,GAAAjC,cAAA,CAAAgC,WAAA;IAA1CE,SAAS,GAAAD,WAAA;IAAEE,YAAY,GAAAF,WAAA;EAC9B,IAAAG,WAAA,GAA8C1D,QAAQ,CAAC,KAAK,CAAC;IAAA2D,WAAA,GAAArC,cAAA,CAAAoC,WAAA;IAAtDE,eAAe,GAAAD,WAAA;IAAEE,kBAAkB,GAAAF,WAAA;EAC1C,IAAAG,WAAA,GAA8C9D,QAAQ,CAAC,EAAE,CAAC;IAAA+D,WAAA,GAAAzC,cAAA,CAAAwC,WAAA;IAAnDE,eAAe,GAAAD,WAAA;IAAEE,kBAAkB,GAAAF,WAAA;EAE1C,IAAMG,eAAe,GAAG,CACtB;IAAEC,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAW,CAAC,EACtC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAW,CAAC,EACvC;IAAED,KAAK,EAAE,QAAQ;IAAEC,KAAK,EAAE;EAAU,CAAC,EACrC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAW,CAAC,CACxC;EAED,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAIC,KAAU,EAAEC,YAAmB,EAAK;IAC5D1B,iBAAiB,CAAC,KAAK,CAAC;IACxB,IAAI0B,YAAY,EAAE;MAChB9B,aAAa,CAAC8B,YAAY,CAAC;IAC7B;EACF,CAAC;EAED,IAAMC,iBAAiB,GAAG,SAApBA,iBAAiBA,CAAA,EAAS;IAE9BpE,KAAK,CAACqE,KAAK,CACT,cAAc,EACd,iCAAiC,EACjC,CACE;MAAEC,IAAI,EAAE,OAAO;MAAEC,KAAK,EAAE;IAAS,CAAC,EAClC;MACED,IAAI,EAAE,OAAO;MACbE,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ;QAEbhD,UAAU,CAAC,cAAc,CAAC;QAC1BJ,cAAc,CAAC,YAAY,CAAC;QAC5ByB,QAAQ,CAAC,cAAc,CAAC;QACxB4B,YAAY,CAAC,uBAAuB,CAAC;MACvC;IACF,CAAC,CAEL,CAAC;EACH,CAAC;EAED,IAAMC,iBAAiB;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;MAEpC,IAAI,CAACzD,WAAW,CAAC0D,IAAI,CAAC,CAAC,EAAE;QACvBJ,YAAY,CAAC,uBAAuB,CAAC;QACrC;MACF;MAEA,IAAI,CAAC9C,QAAQ,IAAImD,QAAQ,CAACnD,QAAQ,CAAC,IAAI,CAAC,EAAE;QACxC8C,YAAY,CAAC,uBAAuB,CAAC;QACrC;MACF;MAEApB,YAAY,CAAC,IAAI,CAAC;MAElB,IAAI;QAEF,MAAM,IAAI0B,OAAO,CAAC,UAAAC,OAAO;UAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;QAAA,EAAC;QAEvD,IAAME,UAAU,GAAG;UACjBC,EAAE,EAAEjD,IAAI,CAACkD,GAAG,CAAC,CAAC,CAACC,QAAQ,CAAC,CAAC;UACzBC,IAAI,EAAEnE,WAAW,CAAC0D,IAAI,CAAC,CAAC;UACxBtD,OAAO,EAAEA,OAAO,CAACsD,IAAI,CAAC,CAAC;UACvBlD,QAAQ,EAAEmD,QAAQ,CAACnD,QAAQ,CAAC;UAC5B4D,QAAQ,EAAExD,gBAAgB;UAC1BK,UAAU,EAAVA,UAAU;UACVQ,KAAK,EAAEA,KAAK,CAACiC,IAAI,CAAC,CAAC;UACnB7B,KAAK,EAAEA,KAAK,CAAC6B,IAAI,CAAC,CAAC;UACnBW,OAAO,EAAE,IAAItD,IAAI,CAAC,CAAC;UACnBuD,MAAM,EAAEC,eAAe,CAACtD,UAAU;QACpC,CAAC;QAEDuD,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEV,UAAU,CAAC;QAGrCW,SAAS,CAAC,CAAC;QACXpB,YAAY,CAAC,uBAAuB,CAAC;MAEvC,CAAC,CAAC,OAAOqB,KAAK,EAAE;QACdrB,YAAY,CAAC,4BAA4B,CAAC;MAC5C,CAAC,SAAS;QACRpB,YAAY,CAAC,KAAK,CAAC;MACrB;IACF,CAAC;IAAA,gBA1CKqB,iBAAiBA,CAAA;MAAA,OAAAC,IAAA,CAAAoB,KAAA,OAAAC,SAAA;IAAA;EAAA,GA0CtB;EAED,IAAMN,eAAe,GAAG,SAAlBA,eAAeA,CAAIO,MAAY,EAAK;IACxC,IAAMC,KAAK,GAAG,IAAIhE,IAAI,CAAC,CAAC;IACxB,IAAMiE,QAAQ,GAAGF,MAAM,CAACG,OAAO,CAAC,CAAC,GAAGF,KAAK,CAACE,OAAO,CAAC,CAAC;IACnD,IAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAE5D,IAAIE,QAAQ,GAAG,CAAC,EAAE,OAAO,SAAS;IAClC,IAAIA,QAAQ,IAAI,CAAC,EAAE,OAAO,SAAS;IACnC,OAAO,OAAO;EAChB,CAAC;EAED,IAAMR,SAAS,GAAG,SAAZA,SAASA,CAAA,EAAS;IACtBzE,cAAc,CAAC,EAAE,CAAC;IAClBI,UAAU,CAAC,EAAE,CAAC;IACdI,WAAW,CAAC,GAAG,CAAC;IAChBI,mBAAmB,CAAC,QAAQ,CAAC;IAC7BK,aAAa,CAAC,IAAIH,IAAI,CAAC,CAAC,CAAC;IACzBW,QAAQ,CAAC,EAAE,CAAC;IACZI,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,IAAMwB,YAAY,GAAG,SAAfA,YAAYA,CAAI+B,OAAe,EAAK;IACxC3C,kBAAkB,CAAC2C,OAAO,CAAC;IAC3B/C,kBAAkB,CAAC,IAAI,CAAC;EAC1B,CAAC;EAED,OACE5C,KAAA,CAACL,YAAY;IAAC+D,KAAK,EAAEkC,MAAM,CAACC,SAAU;IAAAC,QAAA,GACpC9F,KAAA,CAACd,UAAU;MAACwE,KAAK,EAAEkC,MAAM,CAACG,UAAW;MAAAD,QAAA,GACnChG,IAAA,CAACP,IAAI;QAACmE,KAAK,EAAEkC,MAAM,CAACI,IAAK;QAAAF,QAAA,EACvB9F,KAAA,CAACT,IAAI,CAAC0G,OAAO;UAAAH,QAAA,GACXhG,IAAA,CAACV,IAAI;YAACsE,KAAK,EAAEkC,MAAM,CAACM,YAAa;YAAAJ,QAAA,EAAC;UAAc,CAAM,CAAC,EAGvDhG,IAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,2DAAc;YACpBD,KAAK,EAAE5C,WAAY;YACnB6F,YAAY,EAAE5F,cAAe;YAC7BmD,KAAK,EAAEkC,MAAM,CAACQ,KAAM;YACpBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC;UAA4B,CACzC,CAAC,EAGFxG,IAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,6FAAkB;YACxBD,KAAK,EAAEnB,KAAM;YACboE,YAAY,EAAEnE,QAAS;YACvB0B,KAAK,EAAEkC,MAAM,CAACQ,KAAM;YACpBC,IAAI,EAAC,UAAU;YACfC,WAAW,EAAC;UAAY,CACzB,CAAC,EAGFtG,KAAA,CAAChB,IAAI;YAAC0E,KAAK,EAAEkC,MAAM,CAACW,gBAAiB;YAAAT,QAAA,GACnChG,IAAA,CAACT,SAAS;cACR8D,KAAK,EAAC,kDAAU;cAChBD,KAAK,EAAExC,OAAQ;cACfyF,YAAY,EAAExF,UAAW;cACzB+C,KAAK,EAAE,CAACkC,MAAM,CAACQ,KAAK,EAAER,MAAM,CAACY,YAAY,CAAE;cAC3CH,IAAI,EAAC,UAAU;cACfC,WAAW,EAAC,cAAc;cAC1BG,YAAY,EAAC;YAAS,CACvB,CAAC,EACF3G,IAAA,CAACR,MAAM;cACL+G,IAAI,EAAC,WAAW;cAChB1C,OAAO,EAAEJ,iBAAkB;cAC3BG,KAAK,EAAEkC,MAAM,CAACc,UAAW;cACzBC,IAAI,EAAC,iBAAiB;cAAAb,QAAA,EACvB;YAED,CAAQ,CAAC;UAAA,CACL,CAAC,EAGPhG,IAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,wCAAU;YAChBD,KAAK,EAAEpC,QAAS;YAChBqF,YAAY,EAAEpF,WAAY;YAC1B2C,KAAK,EAAEkC,MAAM,CAACQ,KAAM;YACpBC,IAAI,EAAC,UAAU;YACfI,YAAY,EAAC,SAAS;YACtBH,WAAW,EAAC;UAAG,CAChB,CAAC;QAAA,CACU;MAAC,CACX,CAAC,EAEPxG,IAAA,CAACP,IAAI;QAACmE,KAAK,EAAEkC,MAAM,CAACI,IAAK;QAAAF,QAAA,EACvB9F,KAAA,CAACT,IAAI,CAAC0G,OAAO;UAAAH,QAAA,GACXhG,IAAA,CAACV,IAAI;YAACsE,KAAK,EAAEkC,MAAM,CAACM,YAAa;YAAAJ,QAAA,EAAC;UAAY,CAAM,CAAC,EACrDhG,IAAA,CAACN,gBAAgB;YACf0D,KAAK,EAAEhC,gBAAiB;YACxB0F,aAAa,EAAEzF,mBAAoB;YACnC0F,OAAO,EAAE5D,eAAgB;YACzBS,KAAK,EAAEkC,MAAM,CAACkB;UAAgB,CAC/B,CAAC;QAAA,CACU;MAAC,CACX,CAAC,EAEPhH,IAAA,CAACP,IAAI;QAACmE,KAAK,EAAEkC,MAAM,CAACI,IAAK;QAAAF,QAAA,EACvB9F,KAAA,CAACT,IAAI,CAAC0G,OAAO;UAAAH,QAAA,GACXhG,IAAA,CAACV,IAAI;YAACsE,KAAK,EAAEkC,MAAM,CAACM,YAAa;YAAAJ,QAAA,EAAC;UAAqB,CAAM,CAAC,EAE9DhG,IAAA,CAACR,MAAM;YACL+G,IAAI,EAAC,UAAU;YACf1C,OAAO,EAAE,SAATA,OAAOA,CAAA;cAAA,OAAQ/B,iBAAiB,CAAC,IAAI,CAAC;YAAA,CAAC;YACvC8B,KAAK,EAAEkC,MAAM,CAACmB,UAAW;YACzBJ,IAAI,EAAC,UAAU;YAAAb,QAAA,EAEdvE,UAAU,CAACyF,kBAAkB,CAAC,OAAO;UAAC,CACjC,CAAC,EAERrF,cAAc,IACb7B,IAAA,CAACF,cAAc;YACbsD,KAAK,EAAE3B,UAAW;YAClB8E,IAAI,EAAC,MAAM;YACXY,OAAO,EAAC,SAAS;YACjBC,QAAQ,EAAE9D,gBAAiB;YAC3B+D,WAAW,EAAE,IAAI9F,IAAI,CAAC;UAAE,CACzB,CACF,EAEDrB,KAAA,CAAChB,IAAI;YAAC0E,KAAK,EAAEkC,MAAM,CAACwB,aAAc;YAAAtB,QAAA,GAChChG,IAAA,CAACV,IAAI;cAACsE,KAAK,EAAEkC,MAAM,CAACyB,WAAY;cAAAvB,QAAA,EAAC;YAAqB,CAAM,CAAC,EAC7DhG,IAAA,CAACd,IAAI;cAAC0E,KAAK,EAAE,CACXkC,MAAM,CAAC0B,eAAe,EACtB;gBAAEC,eAAe,EAAEC,cAAc,CAAC3C,eAAe,CAACtD,UAAU,CAAC;cAAE,CAAC,CAChE;cAAAuE,QAAA,EACAhG,IAAA,CAACV,IAAI;gBAACsE,KAAK,EAAEkC,MAAM,CAAC6B,UAAW;gBAAA3B,QAAA,EAC5B4B,aAAa,CAAC7C,eAAe,CAACtD,UAAU,CAAC;cAAC,CACvC;YAAC,CACH,CAAC;UAAA,CACH,CAAC;QAAA,CACK;MAAC,CACX,CAAC,EAEPzB,IAAA,CAACP,IAAI;QAACmE,KAAK,EAAEkC,MAAM,CAACI,IAAK;QAAAF,QAAA,EACvB9F,KAAA,CAACT,IAAI,CAAC0G,OAAO;UAAAH,QAAA,GACXhG,IAAA,CAACV,IAAI;YAACsE,KAAK,EAAEkC,MAAM,CAACM,YAAa;YAAAJ,QAAA,EAAC;UAAc,CAAM,CAAC,EACvDhG,IAAA,CAACT,SAAS;YACR8D,KAAK,EAAC,4CAAS;YACfD,KAAK,EAAEf,KAAM;YACbgE,YAAY,EAAE/D,QAAS;YACvBsB,KAAK,EAAEkC,MAAM,CAACQ,KAAM;YACpBC,IAAI,EAAC,UAAU;YACfsB,SAAS;YACTC,aAAa,EAAE,CAAE;YACjBtB,WAAW,EAAC;UAAiC,CAC9C,CAAC;QAAA,CACU;MAAC,CACX,CAAC,EAEPtG,KAAA,CAAChB,IAAI;QAAC0E,KAAK,EAAEkC,MAAM,CAACiC,eAAgB;QAAA/B,QAAA,GAClChG,IAAA,CAACR,MAAM;UACL+G,IAAI,EAAC,WAAW;UAChB1C,OAAO,EAAEE,iBAAkB;UAC3BiE,OAAO,EAAEvF,SAAU;UACnBwF,QAAQ,EAAExF,SAAU;UACpBmB,KAAK,EAAEkC,MAAM,CAACoC,UAAW;UACzBrB,IAAI,EAAC,OAAO;UAAAb,QAAA,EAEXvD,SAAS,GAAG,eAAe,GAAG;QAAY,CACrC,CAAC,EAETzC,IAAA,CAACR,MAAM;UACL+G,IAAI,EAAC,UAAU;UACf1C,OAAO,EAAEqB,SAAU;UACnB+C,QAAQ,EAAExF,SAAU;UACpBmB,KAAK,EAAEkC,MAAM,CAACqC,WAAY;UAC1BtB,IAAI,EAAC,SAAS;UAAAb,QAAA,EACf;QAED,CAAQ,CAAC;MAAA,CACL,CAAC;IAAA,CACG,CAAC,EAEbhG,IAAA,CAACJ,QAAQ;MACPwI,OAAO,EAAEvF,eAAgB;MACzBwF,SAAS,EAAE,SAAXA,SAASA,CAAA;QAAA,OAAQvF,kBAAkB,CAAC,KAAK,CAAC;MAAA,CAAC;MAC3CwF,QAAQ,EAAE,IAAK;MAAAtC,QAAA,EAEd/C;IAAe,CACR,CAAC;EAAA,CACC,CAAC;AAEnB,CAAC;AAED,IAAMyE,cAAc,GAAG,SAAjBA,cAAcA,CAAI5C,MAAc,EAAK;EACzC,QAAQA,MAAM;IACZ,KAAK,OAAO;MAAE,OAAO,SAAS;IAC9B,KAAK,SAAS;MAAE,OAAO,SAAS;IAChC,KAAK,SAAS;MAAE,OAAO,SAAS;IAChC;MAAS,OAAO,SAAS;EAC3B;AACF,CAAC;AAED,IAAM8C,aAAa,GAAG,SAAhBA,aAAaA,CAAI9C,MAAc,EAAK;EACxC,QAAQA,MAAM;IACZ,KAAK,OAAO;MAAE,OAAO,MAAM;IAC3B,KAAK,SAAS;MAAE,OAAO,cAAc;IACrC,KAAK,SAAS;MAAE,OAAO,gBAAgB;IACvC;MAAS,OAAOA,MAAM;EACxB;AACF,CAAC;AAED,IAAMgB,MAAM,GAAG3G,UAAU,CAACoJ,MAAM,CAAC;EAC/BxC,SAAS,EAAE;IACTyC,IAAI,EAAE,CAAC;IACPf,eAAe,EAAE;EACnB,CAAC;EACDxB,UAAU,EAAE;IACVuC,IAAI,EAAE;EACR,CAAC;EACDtC,IAAI,EAAE;IACJuC,MAAM,EAAE,EAAE;IACVC,YAAY,EAAE,CAAC;IACfC,YAAY,EAAE;EAChB,CAAC;EACDvC,YAAY,EAAE;IACZwC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBH,YAAY,EAAE;EAChB,CAAC;EACDpC,KAAK,EAAE;IACLoC,YAAY,EAAE;EAChB,CAAC;EACDjC,gBAAgB,EAAE;IAChBqC,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,UAAU;IACtBC,GAAG,EAAE;EACP,CAAC;EACDtC,YAAY,EAAE;IACZ8B,IAAI,EAAE;EACR,CAAC;EACD5B,UAAU,EAAE;IACV8B,YAAY,EAAE;EAChB,CAAC;EACD1B,eAAe,EAAE;IACf0B,YAAY,EAAE;EAChB,CAAC;EACDzB,UAAU,EAAE;IACVyB,YAAY,EAAE;EAChB,CAAC;EACDpB,aAAa,EAAE;IACbwB,aAAa,EAAE,KAAK;IACpBC,UAAU,EAAE,QAAQ;IACpBE,cAAc,EAAE;EAClB,CAAC;EACD1B,WAAW,EAAE;IACXqB,QAAQ,EAAE,EAAE;IACZM,OAAO,EAAE;EACX,CAAC;EACD1B,eAAe,EAAE;IACf2B,iBAAiB,EAAE,EAAE;IACrBC,eAAe,EAAE,CAAC;IAClBT,YAAY,EAAE;EAChB,CAAC;EACDhB,UAAU,EAAE;IACV0B,KAAK,EAAE,OAAO;IACdT,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE;EACd,CAAC;EACDd,eAAe,EAAE;IACfU,MAAM,EAAE,EAAE;IACVO,GAAG,EAAE;EACP,CAAC;EACDd,UAAU,EAAE;IACVkB,eAAe,EAAE;EACnB,CAAC;EACDjB,WAAW,EAAE;IACXiB,eAAe,EAAE;EACnB;AACF,CAAC,CAAC;AAEF,eAAejJ,wBAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}