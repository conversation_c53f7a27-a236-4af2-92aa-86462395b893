import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
} from 'react-native';
import {
  Text,
  Card,
  Chip,
  FAB,
  Searchbar,
  SegmentedButtons,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';

// بيانات وهمية موسعة
const mockProducts = [
  {
    id: '1',
    name: 'حليب نادك كامل الدسم',
    barcode: '123456789',
    expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),
    status: 'warning' as const,
    location: 'fridge' as const,
    quantity: 2,
    category: 'dairy',
    addedAt: new Date(),
    brand: 'نادك',
  },
  {
    id: '2',
    name: 'خبز التميس الأبيض',
    barcode: '987654321',
    expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),
    status: 'fresh' as const,
    location: 'counter' as const,
    quantity: 1,
    category: 'bakery',
    addedAt: new Date(),
    brand: 'التميس',
  },
  {
    id: '3',
    name: 'زبادي المراعي بالفراولة',
    barcode: '456789123',
    expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),
    status: 'expired' as const,
    location: 'fridge' as const,
    quantity: 3,
    category: 'dairy',
    addedAt: new Date(),
    brand: 'المراعي',
  },
  {
    id: '4',
    name: 'أرز بسمتي',
    barcode: '789123456',
    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),
    status: 'fresh' as const,
    location: 'pantry' as const,
    quantity: 1,
    category: 'grains',
    addedAt: new Date(),
    brand: 'الأرز الذهبي',
  },
  {
    id: '5',
    name: 'عصير برتقال طبيعي',
    barcode: '321654987',
    expiryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),
    status: 'fresh' as const,
    location: 'fridge' as const,
    quantity: 2,
    category: 'beverages',
    addedAt: new Date(),
    brand: 'العصائر الطبيعية',
  },
];

const EnhancedProductsScreen: React.FC = () => {
  const theme = useTheme();
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedFilter, setSelectedFilter] = useState('all');
  const [refreshing, setRefreshing] = useState(false);

  const filterOptions = [
    { value: 'all', label: 'الكل' },
    { value: 'fresh', label: 'طازج' },
    { value: 'warning', label: 'تحذير' },
    { value: 'expired', label: 'منتهي' },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'fresh': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'expired': return '#F44336';
      default: return theme.colors.primary;
    }
  };

  const getLocationIcon = (location: string) => {
    switch (location) {
      case 'fridge': return 'kitchen';
      case 'freezer': return 'ac-unit';
      case 'pantry': return 'store';
      case 'counter': return 'countertops';
      default: return 'inventory';
    }
  };

  const getLocationName = (location: string) => {
    switch (location) {
      case 'fridge': return 'الثلاجة';
      case 'freezer': return 'الفريزر';
      case 'pantry': return 'المخزن';
      case 'counter': return 'المنضدة';
      case 'cabinet': return 'الخزانة';
      default: return 'أخرى';
    }
  };

  const getStatusName = (status: string) => {
    switch (status) {
      case 'fresh': return 'طازج';
      case 'warning': return 'تحذير';
      case 'expired': return 'منتهي الصلاحية';
      default: return status;
    }
  };

  const getDaysUntilExpiry = (expiryDate: Date) => {
    const today = new Date();
    const diffTime = expiryDate.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const filteredProducts = mockProducts.filter(product => {
    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         product.brand.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesFilter = selectedFilter === 'all' || product.status === selectedFilter;
    return matchesSearch && matchesFilter;
  });

  const onRefresh = async () => {
    setRefreshing(true);
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  return (
    <SafeAreaView style={styles.container}>
      <View style={styles.header}>
        <Searchbar
          placeholder="البحث في المنتجات..."
          onChangeText={setSearchQuery}
          value={searchQuery}
          style={styles.searchbar}
        />
        
        <SegmentedButtons
          value={selectedFilter}
          onValueChange={setSelectedFilter}
          buttons={filterOptions}
          style={styles.filterButtons}
        />
      </View>

      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        <Text style={styles.resultsText}>
          {filteredProducts.length} منتج
        </Text>

        {filteredProducts.map((product) => {
          const daysUntilExpiry = getDaysUntilExpiry(product.expiryDate);
          
          return (
            <Card key={product.id} style={styles.productCard}>
              <Card.Content>
                <View style={styles.productHeader}>
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{product.name}</Text>
                    <Text style={styles.productBrand}>{product.brand}</Text>
                    
                    <View style={styles.productMeta}>
                      <View style={styles.metaItem}>
                        <MaterialIcons 
                          name={getLocationIcon(product.location)} 
                          size={16} 
                          color={theme.colors.onSurfaceVariant} 
                        />
                        <Text style={styles.metaText}>
                          {getLocationName(product.location)}
                        </Text>
                      </View>
                      
                      <View style={styles.metaItem}>
                        <MaterialIcons 
                          name="inventory" 
                          size={16} 
                          color={theme.colors.onSurfaceVariant} 
                        />
                        <Text style={styles.metaText}>
                          الكمية: {product.quantity}
                        </Text>
                      </View>
                    </View>
                  </View>
                  
                  <Chip 
                    style={[styles.statusChip, { backgroundColor: getStatusColor(product.status) + '20' }]}
                    textStyle={{ color: getStatusColor(product.status), fontSize: 12 }}
                  >
                    {getStatusName(product.status)}
                  </Chip>
                </View>
                
                <View style={styles.expiryInfo}>
                  <Text style={styles.expiryDate}>
                    انتهاء الصلاحية: {product.expiryDate.toLocaleDateString('ar-SA')}
                  </Text>
                  <Text style={[
                    styles.daysRemaining,
                    { color: getStatusColor(product.status) }
                  ]}>
                    {daysUntilExpiry > 0 
                      ? `${daysUntilExpiry} يوم متبقي`
                      : daysUntilExpiry === 0 
                        ? 'ينتهي اليوم'
                        : `منتهي منذ ${Math.abs(daysUntilExpiry)} يوم`
                    }
                  </Text>
                </View>
              </Card.Content>
            </Card>
          );
        })}

        {filteredProducts.length === 0 && (
          <View style={styles.emptyState}>
            <MaterialIcons name="inventory" size={64} color={theme.colors.onSurfaceVariant} />
            <Text style={styles.emptyText}>لا توجد منتجات</Text>
            <Text style={styles.emptySubtext}>
              {searchQuery ? 'لم يتم العثور على منتجات تطابق البحث' : 'ابدأ بإضافة منتجاتك الأولى'}
            </Text>
          </View>
        )}
      </ScrollView>

      <FAB
        style={styles.fab}
        icon="add"
        onPress={() => {/* navigation.navigate('AddProduct') */}}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  header: {
    padding: 16,
    backgroundColor: 'white',
    elevation: 2,
  },
  searchbar: {
    marginBottom: 12,
  },
  filterButtons: {
    marginBottom: 8,
  },
  scrollView: {
    flex: 1,
  },
  resultsText: {
    margin: 16,
    fontSize: 14,
    opacity: 0.7,
  },
  productCard: {
    marginHorizontal: 16,
    marginBottom: 8,
    borderRadius: 8,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productBrand: {
    fontSize: 14,
    opacity: 0.7,
    marginTop: 2,
  },
  productMeta: {
    flexDirection: 'row',
    marginTop: 8,
    gap: 16,
  },
  metaItem: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  metaText: {
    marginLeft: 4,
    fontSize: 12,
    opacity: 0.7,
  },
  statusChip: {
    marginLeft: 8,
  },
  expiryInfo: {
    marginTop: 12,
    paddingTop: 12,
    borderTopWidth: 1,
    borderTopColor: '#f0f0f0',
  },
  expiryDate: {
    fontSize: 12,
    opacity: 0.7,
  },
  daysRemaining: {
    fontSize: 12,
    fontWeight: 'bold',
    marginTop: 2,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 40,
    marginTop: 60,
  },
  emptyText: {
    fontSize: 18,
    fontWeight: 'bold',
    marginTop: 16,
  },
  emptySubtext: {
    fontSize: 14,
    opacity: 0.7,
    textAlign: 'center',
    marginTop: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default EnhancedProductsScreen;
