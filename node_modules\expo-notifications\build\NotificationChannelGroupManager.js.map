{"version": 3, "file": "NotificationChannelGroupManager.js", "sourceRoot": "", "sources": ["../src/NotificationChannelGroupManager.ts"], "names": [], "mappings": "AAEA,eAAe;IACb,WAAW,EAAE,GAAG,EAAE,GAAE,CAAC;IACrB,eAAe,EAAE,GAAG,EAAE,GAAE,CAAC;CACS,CAAC", "sourcesContent": ["import { NotificationChannelGroupManager } from './NotificationChannelGroupManager.types';\n\nexport default {\n  addListener: () => {},\n  removeListeners: () => {},\n} as NotificationChannelGroupManager;\n"]}