{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"enabled\", \"layout\", \"style\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport * as React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nexport default React.forwardRef(function CardSheet(_ref, ref) {\n  var enabled = _ref.enabled,\n    layout = _ref.layout,\n    style = _ref.style,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _React$useState = React.useState(false),\n    _React$useState2 = _slicedToArray(_React$useState, 2),\n    fill = _React$useState2[0],\n    setFill = _React$useState2[1];\n  var _React$useState3 = React.useState('auto'),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    pointerEvents = _React$useState4[0],\n    setPointerEvents = _React$useState4[1];\n  React.useImperativeHandle(ref, function () {\n    return {\n      setPointerEvents: setPointerEvents\n    };\n  });\n  React.useEffect(function () {\n    if (typeof document === 'undefined' || !document.body) {\n      return;\n    }\n    var width = document.body.clientWidth;\n    var height = document.body.clientHeight;\n    setFill(width === layout.width && height === layout.height);\n  }, [layout.height, layout.width]);\n  return React.createElement(View, _extends({}, rest, {\n    pointerEvents: pointerEvents,\n    style: [enabled && fill ? styles.page : styles.card, style]\n  }));\n});\nvar styles = StyleSheet.create({\n  page: {\n    minHeight: '100%'\n  },\n  card: {\n    flex: 1,\n    overflow: 'hidden'\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "forwardRef", "CardSheet", "_ref", "ref", "enabled", "layout", "style", "rest", "_objectWithoutProperties", "_excluded", "_React$useState", "useState", "_React$useState2", "_slicedToArray", "fill", "setFill", "_React$useState3", "_React$useState4", "pointerEvents", "setPointerEvents", "useImperativeHandle", "useEffect", "document", "body", "width", "clientWidth", "height", "clientHeight", "createElement", "_extends", "styles", "page", "card", "create", "minHeight", "flex", "overflow"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\@react-navigation\\stack\\src\\views\\Stack\\CardSheet.tsx"], "sourcesContent": ["import * as React from 'react';\nimport { StyleSheet, View, ViewProps } from 'react-native';\n\ntype Props = ViewProps & {\n  enabled: boolean;\n  layout: { width: number; height: number };\n  children: React.ReactNode;\n};\n\nexport type CardSheetRef = {\n  setPointerEvents: React.Dispatch<ViewProps['pointerEvents']>;\n};\n\n// This component will render a page which overflows the screen\n// if the container fills the body by comparing the size\n// This lets the document.body handle scrolling of the content\n// It's necessary for mobile browsers to be able to hide address bar on scroll\nexport default React.forwardRef<CardSheetRef, Props>(function CardSheet(\n  { enabled, layout, style, ...rest },\n  ref\n) {\n  const [fill, setFill] = React.useState(false);\n  // To avoid triggering a rerender in Card during animation we had to move\n  // the state to CardSheet. The `setPointerEvents` is then hoisted back to the Card.\n  const [pointerEvents, setPointerEvents] =\n    React.useState<ViewProps['pointerEvents']>('auto');\n\n  React.useImperativeHandle(ref, () => {\n    return { setPointerEvents };\n  });\n\n  React.useEffect(() => {\n    if (typeof document === 'undefined' || !document.body) {\n      // Only run when DOM is available\n      return;\n    }\n\n    const width = document.body.clientWidth;\n    const height = document.body.clientHeight;\n\n    setFill(width === layout.width && height === layout.height);\n  }, [layout.height, layout.width]);\n\n  return (\n    <View\n      {...rest}\n      pointerEvents={pointerEvents}\n      style={[enabled && fill ? styles.page : styles.card, style]}\n    />\n  );\n});\n\nconst styles = StyleSheet.create({\n  page: {\n    minHeight: '100%',\n  },\n  card: {\n    flex: 1,\n    overflow: 'hidden',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AAiB9B,eAAeF,KAAK,CAACG,UAAU,CAAsB,SAASC,SAASA,CAAAC,IAAA,EAErEC,GAAG,EACH;EAAA,IAFEC,OAAO,GAA0BF,IAAA,CAAjCE,OAAO;IAAEC,MAAM,GAAkBH,IAAA,CAAxBG,MAAM;IAAEC,KAAK,GAAWJ,IAAA,CAAhBI,KAAK;IAAKC,IAAA,GAAAC,wBAAA,CAAMN,IAAA,EAAAO,SAAA;EAGnC,IAAAC,eAAA,GAAwBb,KAAK,CAACc,QAAQ,CAAC,KAAK,CAAC;IAAAC,gBAAA,GAAAC,cAAA,CAAAH,eAAA;IAAtCI,IAAI,GAAAF,gBAAA;IAAEG,OAAO,GAAAH,gBAAA;EAGpB,IAAAI,gBAAA,GACEnB,KAAK,CAACc,QAAQ,CAA6B,MAAM,CAAC;IAAAM,gBAAA,GAAAJ,cAAA,CAAAG,gBAAA;IAD7CE,aAAa,GAAAD,gBAAA;IAAEE,gBAAgB,GAAAF,gBAAA;EAGtCpB,KAAK,CAACuB,mBAAmB,CAACjB,GAAG,EAAE,YAAM;IACnC,OAAO;MAAEgB,gBAAA,EAAAA;IAAiB,CAAC;EAC7B,CAAC,CAAC;EAEFtB,KAAK,CAACwB,SAAS,CAAC,YAAM;IACpB,IAAI,OAAOC,QAAQ,KAAK,WAAW,IAAI,CAACA,QAAQ,CAACC,IAAI,EAAE;MAErD;IACF;IAEA,IAAMC,KAAK,GAAGF,QAAQ,CAACC,IAAI,CAACE,WAAW;IACvC,IAAMC,MAAM,GAAGJ,QAAQ,CAACC,IAAI,CAACI,YAAY;IAEzCZ,OAAO,CAACS,KAAK,KAAKnB,MAAM,CAACmB,KAAK,IAAIE,MAAM,KAAKrB,MAAM,CAACqB,MAAM,CAAC;EAC7D,CAAC,EAAE,CAACrB,MAAM,CAACqB,MAAM,EAAErB,MAAM,CAACmB,KAAK,CAAC,CAAC;EAEjC,OACE3B,KAAA,CAAA+B,aAAA,CAAC7B,IAAI,EAAA8B,QAAA,KACCtB,IAAI;IACRW,aAAa,EAAEA,aAAc;IAC7BZ,KAAK,EAAE,CAACF,OAAO,IAAIU,IAAI,GAAGgB,MAAM,CAACC,IAAI,GAAGD,MAAM,CAACE,IAAI,EAAE1B,KAAK;EAAE,GAC5D;AAEN,CAAC,CAAC;AAEF,IAAMwB,MAAM,GAAGhC,UAAU,CAACmC,MAAM,CAAC;EAC/BF,IAAI,EAAE;IACJG,SAAS,EAAE;EACb,CAAC;EACDF,IAAI,EAAE;IACJG,IAAI,EAAE,CAAC;IACPC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}