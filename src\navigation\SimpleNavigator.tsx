import React from 'react';
import { createBottomTabNavigator } from '@react-navigation/bottom-tabs';
import { MaterialIcons } from '@expo/vector-icons';
import { useTheme } from 'react-native-paper';
import { View, Text, StyleSheet } from 'react-native';
import EnhancedHomeScreen from '@/screens/EnhancedHomeScreen';
import EnhancedProductsScreen from '@/screens/EnhancedProductsScreen';
import EnhancedAddProductScreen from '@/screens/EnhancedAddProductScreen';

// شاشات بسيطة للاختبار
const SimpleHomeScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>🏠 الرئيسية</Text>
    <Text style={styles.subtitle}>مرحباً بك في تطبيق رفّك</Text>
  </View>
);

const SimpleProductsScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>📦 المنتجات</Text>
    <Text style={styles.subtitle}>قائمة المنتجات</Text>
  </View>
);

const SimpleAddProductScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>➕ إضافة منتج</Text>
    <Text style={styles.subtitle}>إضافة منتج جديد</Text>
  </View>
);

const SimpleMembersScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>👥 الأعضاء</Text>
    <Text style={styles.subtitle}>إدارة الأعضاء</Text>
  </View>
);

const SimpleProfileScreen = () => (
  <View style={styles.container}>
    <Text style={styles.title}>👤 الملف الشخصي</Text>
    <Text style={styles.subtitle}>إعدادات المستخدم</Text>
  </View>
);

type TabParamList = {
  Home: undefined;
  Products: undefined;
  AddProduct: undefined;
  Members: undefined;
  Profile: undefined;
};

const Tab = createBottomTabNavigator<TabParamList>();

const SimpleNavigator: React.FC = () => {
  const theme = useTheme();

  return (
    <Tab.Navigator
      screenOptions={({ route }) => ({
        tabBarIcon: ({ focused, color, size }) => {
          let iconName: keyof typeof MaterialIcons.glyphMap;

          switch (route.name) {
            case 'Home':
              iconName = 'home';
              break;
            case 'Products':
              iconName = 'inventory';
              break;
            case 'AddProduct':
              iconName = 'add-circle';
              break;
            case 'Members':
              iconName = 'group';
              break;
            case 'Profile':
              iconName = 'person';
              break;
            default:
              iconName = 'help';
          }

          return <MaterialIcons name={iconName} size={size} color={color} />;
        },
        tabBarActiveTintColor: theme.colors.primary,
        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,
        tabBarStyle: {
          backgroundColor: theme.colors.surface,
          borderTopColor: theme.colors.outline,
        },
        headerStyle: {
          backgroundColor: theme.colors.surface,
        },
        headerTintColor: theme.colors.onSurface,
        headerTitleStyle: {
          fontFamily: 'System',
          fontSize: 18,
          fontWeight: 'bold',
        },
      })}
    >
      <Tab.Screen
        name="Home"
        component={EnhancedHomeScreen}
        options={{ title: 'الرئيسية' }}
      />
      <Tab.Screen
        name="Products"
        component={EnhancedProductsScreen}
        options={{ title: 'المنتجات' }}
      />
      <Tab.Screen
        name="AddProduct"
        component={EnhancedAddProductScreen}
        options={{ title: 'إضافة منتج' }}
      />
      <Tab.Screen 
        name="Members" 
        component={SimpleMembersScreen}
        options={{ title: 'الأعضاء' }}
      />
      <Tab.Screen 
        name="Profile" 
        component={SimpleProfileScreen}
        options={{ title: 'الملف الشخصي' }}
      />
    </Tab.Navigator>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  title: {
    fontSize: 24,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  subtitle: {
    fontSize: 16,
    textAlign: 'center',
    opacity: 0.7,
  },
});

export default SimpleNavigator;
