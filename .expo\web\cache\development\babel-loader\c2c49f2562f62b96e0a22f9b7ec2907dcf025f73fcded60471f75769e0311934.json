{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport { initializeApp, getApps } from 'firebase/app';\nimport { getAuth } from 'firebase/auth';\nimport { getFirestore } from 'firebase/firestore';\nimport { getStorage } from 'firebase/storage';\nvar firebaseConfig = {\n  apiKey: \"demo-api-key\",\n  authDomain: \"shelf-track-demo.firebaseapp.com\",\n  projectId: \"shelf-track-demo\",\n  storageBucket: \"shelf-track-demo.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"1:123456789:web:demo-app-id\",\n  measurementId: \"G-DEMO1234\"\n};\nvar app;\nvar auth;\nvar db;\nvar storage;\nexport var initializeFirebase = function () {\n  var _ref = _asyncToGenerator(function* () {\n    try {\n      if (getApps().length === 0) {\n        app = initializeApp(firebaseConfig);\n      } else {\n        app = getApps()[0];\n      }\n      auth = getAuth(app);\n      db = getFirestore(app);\n      storage = getStorage(app);\n      console.log('✅ Firebase initialized successfully');\n      return {\n        app: app,\n        auth: auth,\n        db: db,\n        storage: storage\n      };\n    } catch (error) {\n      console.error('❌ Firebase initialization error:', error);\n      throw error;\n    }\n  });\n  return function initializeFirebase() {\n    return _ref.apply(this, arguments);\n  };\n}();\nexport { auth, db, storage };\nexport default app;\nexport var getFirebaseApp = function getFirebaseApp() {\n  return app;\n};\nexport var getFirebaseAuth = function getFirebaseAuth() {\n  return auth;\n};\nexport var getFirebaseDb = function getFirebaseDb() {\n  return db;\n};\nexport var getFirebaseStorage = function getFirebaseStorage() {\n  return storage;\n};\nexport var COLLECTIONS = {\n  USERS: 'users',\n  GROUPS: 'groups',\n  PRODUCTS: 'products',\n  NOTIFICATIONS: 'notifications',\n  INVITES: 'invites'\n};\nexport var SECURITY_RULES = {\n  users: {\n    read: 'auth != null && auth.uid == resource.data.id',\n    write: 'auth != null && auth.uid == resource.data.id'\n  },\n  groups: {\n    read: 'auth != null && auth.uid in resource.data.memberIds',\n    write: 'auth != null && (auth.uid == resource.data.createdBy || auth.uid in resource.data.adminIds)'\n  },\n  products: {\n    read: 'auth != null && exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) && auth.uid in get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.memberIds',\n    write: 'auth != null && exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) && auth.uid in get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.memberIds'\n  }\n};\nexport var STORAGE_KEYS = {\n  USER_DATA: '@shelf_track_user',\n  SETTINGS: '@shelf_track_settings',\n  OFFLINE_PRODUCTS: '@shelf_track_offline_products',\n  LAST_SYNC: '@shelf_track_last_sync'\n};\nexport var SYNC_CONFIG = {\n  RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000,\n  BATCH_SIZE: 50,\n  SYNC_INTERVAL: 5 * 60 * 1000\n};", "map": {"version": 3, "names": ["initializeApp", "getApps", "getAuth", "getFirestore", "getStorage", "firebaseConfig", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "measurementId", "app", "auth", "db", "storage", "initializeFirebase", "_ref", "_asyncToGenerator", "length", "console", "log", "error", "apply", "arguments", "getFirebaseApp", "getFirebaseAuth", "getFirebaseDb", "getFirebaseStorage", "COLLECTIONS", "USERS", "GROUPS", "PRODUCTS", "NOTIFICATIONS", "INVITES", "SECURITY_RULES", "users", "read", "write", "groups", "products", "STORAGE_KEYS", "USER_DATA", "SETTINGS", "OFFLINE_PRODUCTS", "LAST_SYNC", "SYNC_CONFIG", "RETRY_ATTEMPTS", "RETRY_DELAY", "BATCH_SIZE", "SYNC_INTERVAL"], "sources": ["C:/Users/<USER>/Music/mobile/src/services/firebase.ts"], "sourcesContent": ["// تكوين Firebase\nimport { initializeApp, getApps, FirebaseApp } from 'firebase/app';\nimport { getAuth, Auth } from 'firebase/auth';\nimport { getFirestore, Firestore } from 'firebase/firestore';\nimport { getStorage, FirebaseStorage } from 'firebase/storage';\n\n// تكوين Firebase للتطوير المحلي - يمكن استخدام Firebase Emulator\nconst firebaseConfig = {\n  // إعدادات للتطوير المحلي - يمكن تحديثها لاحقاً\n  apiKey: \"demo-api-key\",\n  authDomain: \"shelf-track-demo.firebaseapp.com\",\n  projectId: \"shelf-track-demo\",\n  storageBucket: \"shelf-track-demo.appspot.com\",\n  messagingSenderId: \"123456789\",\n  appId: \"1:123456789:web:demo-app-id\",\n  measurementId: \"G-DEMO1234\"\n};\n\n// تهيئة Firebase\nlet app: FirebaseApp;\nlet auth: Auth;\nlet db: Firestore;\nlet storage: FirebaseStorage;\n\nexport const initializeFirebase = async () => {\n  try {\n    // التحقق من عدم تهيئة Firebase مسبقاً\n    if (getApps().length === 0) {\n      app = initializeApp(firebaseConfig);\n    } else {\n      app = getApps()[0];\n    }\n\n    // تهيئة الخدمات\n    auth = getAuth(app);\n    db = getFirestore(app);\n    storage = getStorage(app);\n\n    console.log('✅ Firebase initialized successfully');\n    return { app, auth, db, storage };\n  } catch (error) {\n    console.error('❌ Firebase initialization error:', error);\n    throw error;\n  }\n};\n\n// تصدير الخدمات\nexport { auth, db, storage };\nexport default app;\n\n// مساعدات Firebase\nexport const getFirebaseApp = () => app;\nexport const getFirebaseAuth = () => auth;\nexport const getFirebaseDb = () => db;\nexport const getFirebaseStorage = () => storage;\n\n// إعدادات Firestore\nexport const COLLECTIONS = {\n  USERS: 'users',\n  GROUPS: 'groups',\n  PRODUCTS: 'products',\n  NOTIFICATIONS: 'notifications',\n  INVITES: 'invites',\n} as const;\n\n// قواعد الأمان لـ Firestore\nexport const SECURITY_RULES = {\n  // المستخدمون يمكنهم قراءة وكتابة بياناتهم فقط\n  users: {\n    read: 'auth != null && auth.uid == resource.data.id',\n    write: 'auth != null && auth.uid == resource.data.id',\n  },\n  // أعضاء المجموعة يمكنهم قراءة وكتابة بيانات المجموعة\n  groups: {\n    read: 'auth != null && auth.uid in resource.data.memberIds',\n    write: 'auth != null && (auth.uid == resource.data.createdBy || auth.uid in resource.data.adminIds)',\n  },\n  // أعضاء المجموعة يمكنهم قراءة وكتابة المنتجات\n  products: {\n    read: 'auth != null && exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) && auth.uid in get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.memberIds',\n    write: 'auth != null && exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) && auth.uid in get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.memberIds',\n  },\n};\n\n// إعدادات التخزين المحلي\nexport const STORAGE_KEYS = {\n  USER_DATA: '@shelf_track_user',\n  SETTINGS: '@shelf_track_settings',\n  OFFLINE_PRODUCTS: '@shelf_track_offline_products',\n  LAST_SYNC: '@shelf_track_last_sync',\n} as const;\n\n// إعدادات التزامن\nexport const SYNC_CONFIG = {\n  RETRY_ATTEMPTS: 3,\n  RETRY_DELAY: 1000, // 1 ثانية\n  BATCH_SIZE: 50,\n  SYNC_INTERVAL: 5 * 60 * 1000, // 5 دقائق\n} as const;\n"], "mappings": ";AACA,SAASA,aAAa,EAAEC,OAAO,QAAqB,cAAc;AAClE,SAASC,OAAO,QAAc,eAAe;AAC7C,SAASC,YAAY,QAAmB,oBAAoB;AAC5D,SAASC,UAAU,QAAyB,kBAAkB;AAG9D,IAAMC,cAAc,GAAG;EAErBC,MAAM,EAAE,cAAc;EACtBC,UAAU,EAAE,kCAAkC;EAC9CC,SAAS,EAAE,kBAAkB;EAC7BC,aAAa,EAAE,8BAA8B;EAC7CC,iBAAiB,EAAE,WAAW;EAC9BC,KAAK,EAAE,6BAA6B;EACpCC,aAAa,EAAE;AACjB,CAAC;AAGD,IAAIC,GAAgB;AACpB,IAAIC,IAAU;AACd,IAAIC,EAAa;AACjB,IAAIC,OAAwB;AAE5B,OAAO,IAAMC,kBAAkB;EAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;IAC5C,IAAI;MAEF,IAAIlB,OAAO,CAAC,CAAC,CAACmB,MAAM,KAAK,CAAC,EAAE;QAC1BP,GAAG,GAAGb,aAAa,CAACK,cAAc,CAAC;MACrC,CAAC,MAAM;QACLQ,GAAG,GAAGZ,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;MACpB;MAGAa,IAAI,GAAGZ,OAAO,CAACW,GAAG,CAAC;MACnBE,EAAE,GAAGZ,YAAY,CAACU,GAAG,CAAC;MACtBG,OAAO,GAAGZ,UAAU,CAACS,GAAG,CAAC;MAEzBQ,OAAO,CAACC,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO;QAAET,GAAG,EAAHA,GAAG;QAAEC,IAAI,EAAJA,IAAI;QAAEC,EAAE,EAAFA,EAAE;QAAEC,OAAO,EAAPA;MAAQ,CAAC;IACnC,CAAC,CAAC,OAAOO,KAAK,EAAE;MACdF,OAAO,CAACE,KAAK,CAAC,kCAAkC,EAAEA,KAAK,CAAC;MACxD,MAAMA,KAAK;IACb;EACF,CAAC;EAAA,gBApBYN,kBAAkBA,CAAA;IAAA,OAAAC,IAAA,CAAAM,KAAA,OAAAC,SAAA;EAAA;AAAA,GAoB9B;AAGD,SAASX,IAAI,EAAEC,EAAE,EAAEC,OAAO;AAC1B,eAAeH,GAAG;AAGlB,OAAO,IAAMa,cAAc,GAAG,SAAjBA,cAAcA,CAAA;EAAA,OAASb,GAAG;AAAA;AACvC,OAAO,IAAMc,eAAe,GAAG,SAAlBA,eAAeA,CAAA;EAAA,OAASb,IAAI;AAAA;AACzC,OAAO,IAAMc,aAAa,GAAG,SAAhBA,aAAaA,CAAA;EAAA,OAASb,EAAE;AAAA;AACrC,OAAO,IAAMc,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAA;EAAA,OAASb,OAAO;AAAA;AAG/C,OAAO,IAAMc,WAAW,GAAG;EACzBC,KAAK,EAAE,OAAO;EACdC,MAAM,EAAE,QAAQ;EAChBC,QAAQ,EAAE,UAAU;EACpBC,aAAa,EAAE,eAAe;EAC9BC,OAAO,EAAE;AACX,CAAU;AAGV,OAAO,IAAMC,cAAc,GAAG;EAE5BC,KAAK,EAAE;IACLC,IAAI,EAAE,8CAA8C;IACpDC,KAAK,EAAE;EACT,CAAC;EAEDC,MAAM,EAAE;IACNF,IAAI,EAAE,qDAAqD;IAC3DC,KAAK,EAAE;EACT,CAAC;EAEDE,QAAQ,EAAE;IACRH,IAAI,EAAE,8LAA8L;IACpMC,KAAK,EAAE;EACT;AACF,CAAC;AAGD,OAAO,IAAMG,YAAY,GAAG;EAC1BC,SAAS,EAAE,mBAAmB;EAC9BC,QAAQ,EAAE,uBAAuB;EACjCC,gBAAgB,EAAE,+BAA+B;EACjDC,SAAS,EAAE;AACb,CAAU;AAGV,OAAO,IAAMC,WAAW,GAAG;EACzBC,cAAc,EAAE,CAAC;EACjBC,WAAW,EAAE,IAAI;EACjBC,UAAU,EAAE,EAAE;EACdC,aAAa,EAAE,CAAC,GAAG,EAAE,GAAG;AAC1B,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}