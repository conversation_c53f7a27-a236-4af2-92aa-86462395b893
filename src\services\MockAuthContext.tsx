import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import AsyncStorage from '@react-native-async-storage/async-storage';

// أنواع البيانات
export interface User {
  id: string;
  email: string;
  displayName: string;
  photoURL?: string;
  emailVerified: boolean;
  createdAt: Date;
  lastLoginAt: Date;
}

export interface AuthContextType {
  user: User | null;
  isLoading: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  isAuthenticated: boolean;
}

// إنشاء Context
const AuthContext = createContext<AuthContextType | undefined>(undefined);

// مستخدم وهمي للاختبار
const mockUser: User = {
  id: 'mock-user-123',
  email: '<EMAIL>',
  displayName: 'مستخدم تجريبي',
  photoURL: undefined,
  emailVerified: true,
  createdAt: new Date(),
  lastLoginAt: new Date(),
};

// مفاتيح التخزين المحلي
const STORAGE_KEYS = {
  USER: '@shelf_track_mock_user',
  IS_AUTHENTICATED: '@shelf_track_mock_auth',
};

interface AuthProviderProps {
  children: ReactNode;
}

export const MockAuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  // تحميل حالة المصادقة من التخزين المحلي
  useEffect(() => {
    loadAuthState();
  }, []);

  const loadAuthState = async () => {
    try {
      const isAuthenticated = await AsyncStorage.getItem(STORAGE_KEYS.IS_AUTHENTICATED);
      const userData = await AsyncStorage.getItem(STORAGE_KEYS.USER);
      
      if (isAuthenticated === 'true' && userData) {
        const parsedUser = JSON.parse(userData);
        // تحويل التواريخ من string إلى Date
        parsedUser.createdAt = new Date(parsedUser.createdAt);
        parsedUser.lastLoginAt = new Date(parsedUser.lastLoginAt);
        setUser(parsedUser);
      }
    } catch (error) {
      console.error('خطأ في تحميل حالة المصادقة:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const signInWithGoogle = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 1500));
      
      // حفظ بيانات المستخدم
      const updatedUser = {
        ...mockUser,
        lastLoginAt: new Date(),
      };
      
      await AsyncStorage.setItem(STORAGE_KEYS.IS_AUTHENTICATED, 'true');
      await AsyncStorage.setItem(STORAGE_KEYS.USER, JSON.stringify(updatedUser));
      
      setUser(updatedUser);
    } catch (error) {
      console.error('خطأ في تسجيل الدخول:', error);
      throw new Error('فشل في تسجيل الدخول. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  const signOut = async (): Promise<void> => {
    try {
      setIsLoading(true);
      
      // محاكاة تأخير الشبكة
      await new Promise(resolve => setTimeout(resolve, 500));
      
      // مسح بيانات المصادقة
      await AsyncStorage.removeItem(STORAGE_KEYS.IS_AUTHENTICATED);
      await AsyncStorage.removeItem(STORAGE_KEYS.USER);
      
      setUser(null);
    } catch (error) {
      console.error('خطأ في تسجيل الخروج:', error);
      throw new Error('فشل في تسجيل الخروج. يرجى المحاولة مرة أخرى.');
    } finally {
      setIsLoading(false);
    }
  };

  const value: AuthContextType = {
    user,
    isLoading,
    signInWithGoogle,
    signOut,
    isAuthenticated: !!user,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Hook لاستخدام AuthContext
export const useMockAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useMockAuth must be used within a MockAuthProvider');
  }
  return context;
};

// تصدير افتراضي
export default MockAuthProvider;
