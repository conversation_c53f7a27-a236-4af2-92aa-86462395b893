import React, { useEffect, useState } from 'react';
import { StatusBar } from 'expo-status-bar';
import { NavigationContainer } from '@react-navigation/native';
import { createStackNavigator } from '@react-navigation/stack';
import { Provider as PaperProvider } from 'react-native-paper';
import { I18nManager, Platform } from 'react-native';
import * as SplashScreen from 'expo-splash-screen';
import * as Font from 'expo-font';

// الخدمات
import { AuthProvider, useAuth } from '@/services/AuthContext';
import { ProductProvider } from '@/services/ProductContext';
import { NotificationProvider } from '@/services/NotificationContext';
import { initializeFirebase } from '@/services/firebase';

// الشاشات
import AuthScreen from '@/screens/AuthScreen';
import MainNavigator from '@/navigation/MainNavigator';
import SimpleNavigator from '@/navigation/SimpleNavigator';
import LoadingScreen from '@/screens/LoadingScreen';

// الثيم والألوان
import { lightTheme, darkTheme } from '@/constants/theme';
import { useColorScheme } from 'react-native';

// أنواع البيانات
import { RootStackParamList } from '@/types';

const Stack = createStackNavigator<RootStackParamList>();

// منع إخفاء شاشة التحميل تلقائياً
SplashScreen.preventAutoHideAsync();

// تفعيل RTL للغة العربية
if (Platform.OS !== 'web') {
  I18nManager.allowRTL(true);
  I18nManager.forceRTL(true);
}

function AppContent() {
  const { user, isLoading } = useAuth();
  const colorScheme = useColorScheme();
  const [appIsReady, setAppIsReady] = useState(false);

  useEffect(() => {
    async function prepare() {
      try {
        // تحميل الخطوط
        await Font.loadAsync({
          // يمكن إضافة خطوط عربية مخصصة هنا
        });

        // تهيئة Firebase
        await initializeFirebase();

        // محاكاة وقت التحميل
        await new Promise(resolve => setTimeout(resolve, 2000));
      } catch (e) {
        console.warn(e);
      } finally {
        setAppIsReady(true);
      }
    }

    prepare();
  }, []);

  useEffect(() => {
    if (appIsReady) {
      SplashScreen.hideAsync();
    }
  }, [appIsReady]);

  if (!appIsReady || isLoading) {
    return <LoadingScreen />;
  }

  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer theme={theme}>
        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
        <Stack.Navigator
          screenOptions={{
            headerShown: false,
            gestureEnabled: true,
            gestureDirection: 'horizontal-inverted', // RTL gesture
          }}
        >
          {user ? (
            <Stack.Screen name="Main" component={MainNavigator} />
          ) : (
            <Stack.Screen name="Auth" component={AuthScreen} />
          )}
        </Stack.Navigator>
      </NavigationContainer>
    </PaperProvider>
  );
}

// نسخة مبسطة للاختبار بدون Firebase
function SimpleApp() {
  const colorScheme = useColorScheme();
  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;

  return (
    <PaperProvider theme={theme}>
      <NavigationContainer theme={theme}>
        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />
        <SimpleNavigator />
      </NavigationContainer>
    </PaperProvider>
  );
}

export default function App() {
  // استخدام النسخة المبسطة للاختبار
  return <SimpleApp />;

  // النسخة الكاملة مع Firebase (معطلة مؤقتاً)
  /*
  return (
    <AuthProvider>
      <ProductProvider>
        <NotificationProvider>
          <AppContent />
        </NotificationProvider>
      </ProductProvider>
    </AuthProvider>
  );
  */
}
