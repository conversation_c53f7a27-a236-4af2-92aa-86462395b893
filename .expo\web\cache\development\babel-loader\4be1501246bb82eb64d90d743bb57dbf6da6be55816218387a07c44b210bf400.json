{"ast": null, "code": "import React from 'react';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { useTheme } from 'react-native-paper';\nimport View from \"react-native-web/dist/exports/View\";\nimport Text from \"react-native-web/dist/exports/Text\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport EnhancedHomeScreen from \"../screens/EnhancedHomeScreen\";\nimport EnhancedProductsScreen from \"../screens/EnhancedProductsScreen\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar SimpleHomeScreen = function SimpleHomeScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"\\uD83C\\uDFE0 \\u0627\\u0644\\u0631\\u0626\\u064A\\u0633\\u064A\\u0629\"\n    }), _jsx(Text, {\n      style: styles.subtitle,\n      children: \"\\u0645\\u0631\\u062D\\u0628\\u0627\\u064B \\u0628\\u0643 \\u0641\\u064A \\u062A\\u0637\\u0628\\u064A\\u0642 \\u0631\\u0641\\u0651\\u0643\"\n    })]\n  });\n};\nvar SimpleProductsScreen = function SimpleProductsScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"\\uD83D\\uDCE6 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n    }), _jsx(Text, {\n      style: styles.subtitle,\n      children: \"\\u0642\\u0627\\u0626\\u0645\\u0629 \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n    })]\n  });\n};\nvar SimpleAddProductScreen = function SimpleAddProductScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"\\u2795 \\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u062A\\u062C\"\n    }), _jsx(Text, {\n      style: styles.subtitle,\n      children: \"\\u0625\\u0636\\u0627\\u0641\\u0629 \\u0645\\u0646\\u062A\\u062C \\u062C\\u062F\\u064A\\u062F\"\n    })]\n  });\n};\nvar SimpleMembersScreen = function SimpleMembersScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"\\uD83D\\uDC65 \\u0627\\u0644\\u0623\\u0639\\u0636\\u0627\\u0621\"\n    }), _jsx(Text, {\n      style: styles.subtitle,\n      children: \"\\u0625\\u062F\\u0627\\u0631\\u0629 \\u0627\\u0644\\u0623\\u0639\\u0636\\u0627\\u0621\"\n    })]\n  });\n};\nvar SimpleProfileScreen = function SimpleProfileScreen() {\n  return _jsxs(View, {\n    style: styles.container,\n    children: [_jsx(Text, {\n      style: styles.title,\n      children: \"\\uD83D\\uDC64 \\u0627\\u0644\\u0645\\u0644\\u0641 \\u0627\\u0644\\u0634\\u062E\\u0635\\u064A\"\n    }), _jsx(Text, {\n      style: styles.subtitle,\n      children: \"\\u0625\\u0639\\u062F\\u0627\\u062F\\u0627\\u062A \\u0627\\u0644\\u0645\\u0633\\u062A\\u062E\\u062F\\u0645\"\n    })]\n  });\n};\nvar Tab = createBottomTabNavigator();\nvar SimpleNavigator = function SimpleNavigator() {\n  var theme = useTheme();\n  return _jsxs(Tab.Navigator, {\n    screenOptions: function screenOptions(_ref) {\n      var route = _ref.route;\n      return {\n        tabBarIcon: function tabBarIcon(_ref2) {\n          var focused = _ref2.focused,\n            color = _ref2.color,\n            size = _ref2.size;\n          var iconName;\n          switch (route.name) {\n            case 'Home':\n              iconName = 'home';\n              break;\n            case 'Products':\n              iconName = 'inventory';\n              break;\n            case 'AddProduct':\n              iconName = 'add-circle';\n              break;\n            case 'Members':\n              iconName = 'group';\n              break;\n            case 'Profile':\n              iconName = 'person';\n              break;\n            default:\n              iconName = 'help';\n          }\n          return _jsx(MaterialIcons, {\n            name: iconName,\n            size: size,\n            color: color\n          });\n        },\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,\n        tabBarStyle: {\n          backgroundColor: theme.colors.surface,\n          borderTopColor: theme.colors.outline\n        },\n        headerStyle: {\n          backgroundColor: theme.colors.surface\n        },\n        headerTintColor: theme.colors.onSurface,\n        headerTitleStyle: {\n          fontFamily: 'System',\n          fontSize: 18,\n          fontWeight: 'bold'\n        }\n      };\n    },\n    children: [_jsx(Tab.Screen, {\n      name: \"Home\",\n      component: EnhancedHomeScreen,\n      options: {\n        title: 'الرئيسية'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"Products\",\n      component: EnhancedProductsScreen,\n      options: {\n        title: 'المنتجات'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"AddProduct\",\n      component: SimpleAddProductScreen,\n      options: {\n        title: 'إضافة منتج'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"Members\",\n      component: SimpleMembersScreen,\n      options: {\n        title: 'الأعضاء'\n      }\n    }), _jsx(Tab.Screen, {\n      name: \"Profile\",\n      component: SimpleProfileScreen,\n      options: {\n        title: 'الملف الشخصي'\n      }\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 10,\n    textAlign: 'center'\n  },\n  subtitle: {\n    fontSize: 16,\n    textAlign: 'center',\n    opacity: 0.7\n  }\n});\nexport default SimpleNavigator;", "map": {"version": 3, "names": ["React", "createBottomTabNavigator", "MaterialIcons", "useTheme", "View", "Text", "StyleSheet", "EnhancedHomeScreen", "EnhancedProductsScreen", "jsx", "_jsx", "jsxs", "_jsxs", "SimpleHomeScreen", "style", "styles", "container", "children", "title", "subtitle", "SimpleProductsScreen", "SimpleAddProductScreen", "SimpleMembersScreen", "SimpleProfileScreen", "Tab", "SimpleNavigator", "theme", "Navigator", "screenOptions", "_ref", "route", "tabBarIcon", "_ref2", "focused", "color", "size", "iconName", "name", "tabBarActiveTintColor", "colors", "primary", "tabBarInactiveTintColor", "onSurfaceVariant", "tabBarStyle", "backgroundColor", "surface", "borderTopColor", "outline", "headerStyle", "headerTintColor", "onSurface", "headerTitleStyle", "fontFamily", "fontSize", "fontWeight", "Screen", "component", "options", "create", "flex", "justifyContent", "alignItems", "padding", "marginBottom", "textAlign", "opacity"], "sources": ["C:/Users/<USER>/Music/mobile/src/navigation/SimpleNavigator.tsx"], "sourcesContent": ["import React from 'react';\nimport { createBottomTabNavigator } from '@react-navigation/bottom-tabs';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { useTheme } from 'react-native-paper';\nimport { View, Text, StyleSheet } from 'react-native';\nimport EnhancedHomeScreen from '@/screens/EnhancedHomeScreen';\nimport EnhancedProductsScreen from '@/screens/EnhancedProductsScreen';\nimport EnhancedAddProductScreen from '@/screens/EnhancedAddProductScreen';\n\n// شاشات بسيطة للاختبار\nconst SimpleHomeScreen = () => (\n  <View style={styles.container}>\n    <Text style={styles.title}>🏠 الرئيسية</Text>\n    <Text style={styles.subtitle}>مرحباً بك في تطبيق رفّك</Text>\n  </View>\n);\n\nconst SimpleProductsScreen = () => (\n  <View style={styles.container}>\n    <Text style={styles.title}>📦 المنتجات</Text>\n    <Text style={styles.subtitle}>قائمة المنتجات</Text>\n  </View>\n);\n\nconst SimpleAddProductScreen = () => (\n  <View style={styles.container}>\n    <Text style={styles.title}>➕ إضافة منتج</Text>\n    <Text style={styles.subtitle}>إضافة منتج جديد</Text>\n  </View>\n);\n\nconst SimpleMembersScreen = () => (\n  <View style={styles.container}>\n    <Text style={styles.title}>👥 الأعضاء</Text>\n    <Text style={styles.subtitle}>إدارة الأعضاء</Text>\n  </View>\n);\n\nconst SimpleProfileScreen = () => (\n  <View style={styles.container}>\n    <Text style={styles.title}>👤 الملف الشخصي</Text>\n    <Text style={styles.subtitle}>إعدادات المستخدم</Text>\n  </View>\n);\n\ntype TabParamList = {\n  Home: undefined;\n  Products: undefined;\n  AddProduct: undefined;\n  Members: undefined;\n  Profile: undefined;\n};\n\nconst Tab = createBottomTabNavigator<TabParamList>();\n\nconst SimpleNavigator: React.FC = () => {\n  const theme = useTheme();\n\n  return (\n    <Tab.Navigator\n      screenOptions={({ route }) => ({\n        tabBarIcon: ({ focused, color, size }) => {\n          let iconName: keyof typeof MaterialIcons.glyphMap;\n\n          switch (route.name) {\n            case 'Home':\n              iconName = 'home';\n              break;\n            case 'Products':\n              iconName = 'inventory';\n              break;\n            case 'AddProduct':\n              iconName = 'add-circle';\n              break;\n            case 'Members':\n              iconName = 'group';\n              break;\n            case 'Profile':\n              iconName = 'person';\n              break;\n            default:\n              iconName = 'help';\n          }\n\n          return <MaterialIcons name={iconName} size={size} color={color} />;\n        },\n        tabBarActiveTintColor: theme.colors.primary,\n        tabBarInactiveTintColor: theme.colors.onSurfaceVariant,\n        tabBarStyle: {\n          backgroundColor: theme.colors.surface,\n          borderTopColor: theme.colors.outline,\n        },\n        headerStyle: {\n          backgroundColor: theme.colors.surface,\n        },\n        headerTintColor: theme.colors.onSurface,\n        headerTitleStyle: {\n          fontFamily: 'System',\n          fontSize: 18,\n          fontWeight: 'bold',\n        },\n      })}\n    >\n      <Tab.Screen\n        name=\"Home\"\n        component={EnhancedHomeScreen}\n        options={{ title: 'الرئيسية' }}\n      />\n      <Tab.Screen\n        name=\"Products\"\n        component={EnhancedProductsScreen}\n        options={{ title: 'المنتجات' }}\n      />\n      <Tab.Screen \n        name=\"AddProduct\" \n        component={SimpleAddProductScreen}\n        options={{ title: 'إضافة منتج' }}\n      />\n      <Tab.Screen \n        name=\"Members\" \n        component={SimpleMembersScreen}\n        options={{ title: 'الأعضاء' }}\n      />\n      <Tab.Screen \n        name=\"Profile\" \n        component={SimpleProfileScreen}\n        options={{ title: 'الملف الشخصي' }}\n      />\n    </Tab.Navigator>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    justifyContent: 'center',\n    alignItems: 'center',\n    padding: 20,\n  },\n  title: {\n    fontSize: 24,\n    fontWeight: 'bold',\n    marginBottom: 10,\n    textAlign: 'center',\n  },\n  subtitle: {\n    fontSize: 16,\n    textAlign: 'center',\n    opacity: 0.7,\n  },\n});\n\nexport default SimpleNavigator;\n"], "mappings": "AAAA,OAAOA,KAAK,MAAM,OAAO;AACzB,SAASC,wBAAwB,QAAQ,+BAA+B;AACxE,SAASC,aAAa,QAAQ,oBAAoB;AAClD,SAASC,QAAQ,QAAQ,oBAAoB;AAAC,OAAAC,IAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAE9C,OAAOC,kBAAkB;AACzB,OAAOC,sBAAsB;AAAyC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAItE,IAAMC,gBAAgB,GAAG,SAAnBA,gBAAgBA,CAAA;EAAA,OACpBD,KAAA,CAACR,IAAI;IAACU,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAW,CAAM,CAAC,EAC7CP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAAC;IAAuB,CAAM,CAAC;EAAA,CACxD,CAAC;AAAA,CACR;AAED,IAAMG,oBAAoB,GAAG,SAAvBA,oBAAoBA,CAAA;EAAA,OACxBR,KAAA,CAACR,IAAI;IAACU,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAW,CAAM,CAAC,EAC7CP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAAC;IAAc,CAAM,CAAC;EAAA,CAC/C,CAAC;AAAA,CACR;AAED,IAAMI,sBAAsB,GAAG,SAAzBA,sBAAsBA,CAAA;EAAA,OAC1BT,KAAA,CAACR,IAAI;IAACU,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAY,CAAM,CAAC,EAC9CP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAAC;IAAe,CAAM,CAAC;EAAA,CAChD,CAAC;AAAA,CACR;AAED,IAAMK,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OACvBV,KAAA,CAACR,IAAI;IAACU,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAU,CAAM,CAAC,EAC5CP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAAC;IAAa,CAAM,CAAC;EAAA,CAC9C,CAAC;AAAA,CACR;AAED,IAAMM,mBAAmB,GAAG,SAAtBA,mBAAmBA,CAAA;EAAA,OACvBX,KAAA,CAACR,IAAI;IAACU,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GAC5BP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACG,KAAM;MAAAD,QAAA,EAAC;IAAe,CAAM,CAAC,EACjDP,IAAA,CAACL,IAAI;MAACS,KAAK,EAAEC,MAAM,CAACI,QAAS;MAAAF,QAAA,EAAC;IAAgB,CAAM,CAAC;EAAA,CACjD,CAAC;AAAA,CACR;AAUD,IAAMO,GAAG,GAAGvB,wBAAwB,CAAe,CAAC;AAEpD,IAAMwB,eAAyB,GAAG,SAA5BA,eAAyBA,CAAA,EAAS;EACtC,IAAMC,KAAK,GAAGvB,QAAQ,CAAC,CAAC;EAExB,OACES,KAAA,CAACY,GAAG,CAACG,SAAS;IACZC,aAAa,EAAE,SAAfA,aAAaA,CAAAC,IAAA;MAAA,IAAKC,KAAK,GAAAD,IAAA,CAALC,KAAK;MAAA,OAAQ;QAC7BC,UAAU,EAAE,SAAZA,UAAUA,CAAAC,KAAA,EAAgC;UAAA,IAA3BC,OAAO,GAAAD,KAAA,CAAPC,OAAO;YAAEC,KAAK,GAAAF,KAAA,CAALE,KAAK;YAAEC,IAAI,GAAAH,KAAA,CAAJG,IAAI;UACjC,IAAIC,QAA6C;UAEjD,QAAQN,KAAK,CAACO,IAAI;YAChB,KAAK,MAAM;cACTD,QAAQ,GAAG,MAAM;cACjB;YACF,KAAK,UAAU;cACbA,QAAQ,GAAG,WAAW;cACtB;YACF,KAAK,YAAY;cACfA,QAAQ,GAAG,YAAY;cACvB;YACF,KAAK,SAAS;cACZA,QAAQ,GAAG,OAAO;cAClB;YACF,KAAK,SAAS;cACZA,QAAQ,GAAG,QAAQ;cACnB;YACF;cACEA,QAAQ,GAAG,MAAM;UACrB;UAEA,OAAO1B,IAAA,CAACR,aAAa;YAACmC,IAAI,EAAED,QAAS;YAACD,IAAI,EAAEA,IAAK;YAACD,KAAK,EAAEA;UAAM,CAAE,CAAC;QACpE,CAAC;QACDI,qBAAqB,EAAEZ,KAAK,CAACa,MAAM,CAACC,OAAO;QAC3CC,uBAAuB,EAAEf,KAAK,CAACa,MAAM,CAACG,gBAAgB;QACtDC,WAAW,EAAE;UACXC,eAAe,EAAElB,KAAK,CAACa,MAAM,CAACM,OAAO;UACrCC,cAAc,EAAEpB,KAAK,CAACa,MAAM,CAACQ;QAC/B,CAAC;QACDC,WAAW,EAAE;UACXJ,eAAe,EAAElB,KAAK,CAACa,MAAM,CAACM;QAChC,CAAC;QACDI,eAAe,EAAEvB,KAAK,CAACa,MAAM,CAACW,SAAS;QACvCC,gBAAgB,EAAE;UAChBC,UAAU,EAAE,QAAQ;UACpBC,QAAQ,EAAE,EAAE;UACZC,UAAU,EAAE;QACd;MACF,CAAC;IAAA,CAAE;IAAArC,QAAA,GAEHP,IAAA,CAACc,GAAG,CAAC+B,MAAM;MACTlB,IAAI,EAAC,MAAM;MACXmB,SAAS,EAAEjD,kBAAmB;MAC9BkD,OAAO,EAAE;QAAEvC,KAAK,EAAE;MAAW;IAAE,CAChC,CAAC,EACFR,IAAA,CAACc,GAAG,CAAC+B,MAAM;MACTlB,IAAI,EAAC,UAAU;MACfmB,SAAS,EAAEhD,sBAAuB;MAClCiD,OAAO,EAAE;QAAEvC,KAAK,EAAE;MAAW;IAAE,CAChC,CAAC,EACFR,IAAA,CAACc,GAAG,CAAC+B,MAAM;MACTlB,IAAI,EAAC,YAAY;MACjBmB,SAAS,EAAEnC,sBAAuB;MAClCoC,OAAO,EAAE;QAAEvC,KAAK,EAAE;MAAa;IAAE,CAClC,CAAC,EACFR,IAAA,CAACc,GAAG,CAAC+B,MAAM;MACTlB,IAAI,EAAC,SAAS;MACdmB,SAAS,EAAElC,mBAAoB;MAC/BmC,OAAO,EAAE;QAAEvC,KAAK,EAAE;MAAU;IAAE,CAC/B,CAAC,EACFR,IAAA,CAACc,GAAG,CAAC+B,MAAM;MACTlB,IAAI,EAAC,SAAS;MACdmB,SAAS,EAAEjC,mBAAoB;MAC/BkC,OAAO,EAAE;QAAEvC,KAAK,EAAE;MAAe;IAAE,CACpC,CAAC;EAAA,CACW,CAAC;AAEpB,CAAC;AAED,IAAMH,MAAM,GAAGT,UAAU,CAACoD,MAAM,CAAC;EAC/B1C,SAAS,EAAE;IACT2C,IAAI,EAAE,CAAC;IACPC,cAAc,EAAE,QAAQ;IACxBC,UAAU,EAAE,QAAQ;IACpBC,OAAO,EAAE;EACX,CAAC;EACD5C,KAAK,EAAE;IACLmC,QAAQ,EAAE,EAAE;IACZC,UAAU,EAAE,MAAM;IAClBS,YAAY,EAAE,EAAE;IAChBC,SAAS,EAAE;EACb,CAAC;EACD7C,QAAQ,EAAE;IACRkC,QAAQ,EAAE,EAAE;IACZW,SAAS,EAAE,QAAQ;IACnBC,OAAO,EAAE;EACX;AACF,CAAC,CAAC;AAEF,eAAexC,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}