{"ast": null, "code": "import React from 'react';\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport View from \"react-native-web/dist/exports/View\";\nimport Badge from \"./Badge\";\nexport default function TabBarIcon(_ref) {\n  var _ = _ref.route,\n    horizontal = _ref.horizontal,\n    badge = _ref.badge,\n    badgeStyle = _ref.badgeStyle,\n    activeOpacity = _ref.activeOpacity,\n    inactiveOpacity = _ref.inactiveOpacity,\n    activeTintColor = _ref.activeTintColor,\n    inactiveTintColor = _ref.inactiveTintColor,\n    renderIcon = _ref.renderIcon,\n    style = _ref.style;\n  var size = 25;\n  return React.createElement(View, {\n    style: [horizontal ? styles.iconHorizontal : styles.iconVertical, style]\n  }, React.createElement(View, {\n    style: [styles.icon, {\n      opacity: activeOpacity\n    }]\n  }, renderIcon({\n    focused: true,\n    size: size,\n    color: activeTintColor\n  })), React.createElement(View, {\n    style: [styles.icon, {\n      opacity: inactiveOpacity\n    }]\n  }, renderIcon({\n    focused: false,\n    size: size,\n    color: inactiveTintColor\n  })), React.createElement(Badge, {\n    visible: badge != null,\n    style: [styles.badge, horizontal ? styles.badgeHorizontal : styles.badgeVertical, badgeStyle],\n    size: size * 3 / 4\n  }, badge));\n}\nvar styles = StyleSheet.create({\n  icon: {\n    position: 'absolute',\n    alignSelf: 'center',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: '100%',\n    width: '100%',\n    minWidth: 25\n  },\n  iconVertical: {\n    flex: 1\n  },\n  iconHorizontal: {\n    height: '100%',\n    marginTop: 3\n  },\n  badge: {\n    position: 'absolute',\n    left: 3\n  },\n  badgeVertical: {\n    top: 3\n  },\n  badgeHorizontal: {\n    top: 7\n  }\n});", "map": {"version": 3, "names": ["React", "StyleSheet", "View", "Badge", "TabBarIcon", "_ref", "_", "route", "horizontal", "badge", "badgeStyle", "activeOpacity", "inactiveOpacity", "activeTintColor", "inactiveTintColor", "renderIcon", "style", "size", "createElement", "styles", "iconHorizontal", "iconVertical", "icon", "opacity", "focused", "color", "visible", "badgeHorizontal", "badgeVertical", "create", "position", "alignSelf", "alignItems", "justifyContent", "height", "width", "min<PERSON><PERSON><PERSON>", "flex", "marginTop", "left", "top"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\@react-navigation\\bottom-tabs\\src\\views\\TabBarIcon.tsx"], "sourcesContent": ["import type { Route } from '@react-navigation/native';\nimport React from 'react';\nimport {\n  StyleProp,\n  StyleSheet,\n  TextStyle,\n  View,\n  ViewStyle,\n} from 'react-native';\n\nimport Badge from './Badge';\n\ntype Props = {\n  route: Route<string>;\n  horizontal: boolean;\n  badge?: string | number;\n  badgeStyle?: StyleProp<TextStyle>;\n  activeOpacity: number;\n  inactiveOpacity: number;\n  activeTintColor: string;\n  inactiveTintColor: string;\n  renderIcon: (props: {\n    focused: boolean;\n    color: string;\n    size: number;\n  }) => React.ReactNode;\n  style: StyleProp<ViewStyle>;\n};\n\nexport default function TabBarIcon({\n  route: _,\n  horizontal,\n  badge,\n  badgeStyle,\n  activeOpacity,\n  inactiveOpacity,\n  activeTintColor,\n  inactiveTintColor,\n  renderIcon,\n  style,\n}: Props) {\n  const size = 25;\n\n  // We render the icon twice at the same position on top of each other:\n  // active and inactive one, so we can fade between them.\n  return (\n    <View\n      style={[horizontal ? styles.iconHorizontal : styles.iconVertical, style]}\n    >\n      <View style={[styles.icon, { opacity: activeOpacity }]}>\n        {renderIcon({\n          focused: true,\n          size,\n          color: activeTintColor,\n        })}\n      </View>\n      <View style={[styles.icon, { opacity: inactiveOpacity }]}>\n        {renderIcon({\n          focused: false,\n          size,\n          color: inactiveTintColor,\n        })}\n      </View>\n      <Badge\n        visible={badge != null}\n        style={[\n          styles.badge,\n          horizontal ? styles.badgeHorizontal : styles.badgeVertical,\n          badgeStyle,\n        ]}\n        size={(size * 3) / 4}\n      >\n        {badge}\n      </Badge>\n    </View>\n  );\n}\n\nconst styles = StyleSheet.create({\n  icon: {\n    // We render the icon twice at the same position on top of each other:\n    // active and inactive one, so we can fade between them:\n    // Cover the whole iconContainer:\n    position: 'absolute',\n    alignSelf: 'center',\n    alignItems: 'center',\n    justifyContent: 'center',\n    height: '100%',\n    width: '100%',\n    // Workaround for react-native >= 0.54 layout bug\n    minWidth: 25,\n  },\n  iconVertical: {\n    flex: 1,\n  },\n  iconHorizontal: {\n    height: '100%',\n    marginTop: 3,\n  },\n  badge: {\n    position: 'absolute',\n    left: 3,\n  },\n  badgeVertical: {\n    top: 3,\n  },\n  badgeHorizontal: {\n    top: 7,\n  },\n});\n"], "mappings": "AACA,OAAOA,KAAK,MAAM,OAAO;AAAA,OAAAC,UAAA;AAAA,OAAAC,IAAA;AASzB,OAAOC,KAAK;AAmBZ,eAAe,SAASC,UAAUA,CAAAC,IAAA,EAWxB;EAAA,IAVDC,CAAC,GAUFD,IAAA,CAVNE,KAAK;IACLC,UAAU,GASJH,IAAA,CATNG,UAAU;IACVC,KAAK,GAQCJ,IAAA,CARNI,KAAK;IACLC,UAAU,GAOJL,IAAA,CAPNK,UAAU;IACVC,aAAa,GAMPN,IAAA,CANNM,aAAa;IACbC,eAAe,GAKTP,IAAA,CALNO,eAAe;IACfC,eAAe,GAITR,IAAA,CAJNQ,eAAe;IACfC,iBAAiB,GAGXT,IAAA,CAHNS,iBAAiB;IACjBC,UAAU,GAEJV,IAAA,CAFNU,UAAU;IACVC,KAAA,GACMX,IAAA,CADNW,KAAA;EAEA,IAAMC,IAAI,GAAG,EAAE;EAIf,OACEjB,KAAA,CAAAkB,aAAA,CAAChB,IAAI;IACHc,KAAK,EAAE,CAACR,UAAU,GAAGW,MAAM,CAACC,cAAc,GAAGD,MAAM,CAACE,YAAY,EAAEL,KAAK;EAAE,GAEzEhB,KAAA,CAAAkB,aAAA,CAAChB,IAAI;IAACc,KAAK,EAAE,CAACG,MAAM,CAACG,IAAI,EAAE;MAAEC,OAAO,EAAEZ;IAAc,CAAC;EAAE,GACpDI,UAAU,CAAC;IACVS,OAAO,EAAE,IAAI;IACbP,IAAI,EAAJA,IAAI;IACJQ,KAAK,EAAEZ;EACT,CAAC,CAAC,CACG,EACPb,KAAA,CAAAkB,aAAA,CAAChB,IAAI;IAACc,KAAK,EAAE,CAACG,MAAM,CAACG,IAAI,EAAE;MAAEC,OAAO,EAAEX;IAAgB,CAAC;EAAE,GACtDG,UAAU,CAAC;IACVS,OAAO,EAAE,KAAK;IACdP,IAAI,EAAJA,IAAI;IACJQ,KAAK,EAAEX;EACT,CAAC,CAAC,CACG,EACPd,KAAA,CAAAkB,aAAA,CAACf,KAAK;IACJuB,OAAO,EAAEjB,KAAK,IAAI,IAAK;IACvBO,KAAK,EAAE,CACLG,MAAM,CAACV,KAAK,EACZD,UAAU,GAAGW,MAAM,CAACQ,eAAe,GAAGR,MAAM,CAACS,aAAa,EAC1DlB,UAAU,CACV;IACFO,IAAI,EAAGA,IAAI,GAAG,CAAC,GAAI;EAAE,GAEpBR,KAAK,CACA,CACH;AAEX;AAEA,IAAMU,MAAM,GAAGlB,UAAU,CAAC4B,MAAM,CAAC;EAC/BP,IAAI,EAAE;IAIJQ,QAAQ,EAAE,UAAU;IACpBC,SAAS,EAAE,QAAQ;IACnBC,UAAU,EAAE,QAAQ;IACpBC,cAAc,EAAE,QAAQ;IACxBC,MAAM,EAAE,MAAM;IACdC,KAAK,EAAE,MAAM;IAEbC,QAAQ,EAAE;EACZ,CAAC;EACDf,YAAY,EAAE;IACZgB,IAAI,EAAE;EACR,CAAC;EACDjB,cAAc,EAAE;IACdc,MAAM,EAAE,MAAM;IACdI,SAAS,EAAE;EACb,CAAC;EACD7B,KAAK,EAAE;IACLqB,QAAQ,EAAE,UAAU;IACpBS,IAAI,EAAE;EACR,CAAC;EACDX,aAAa,EAAE;IACbY,GAAG,EAAE;EACP,CAAC;EACDb,eAAe,EAAE;IACfa,GAAG,EAAE;EACP;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}