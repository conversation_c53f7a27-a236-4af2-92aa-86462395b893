{"version": 3, "file": "BadgeModule.types.js", "sourceRoot": "", "sources": ["../src/BadgeModule.types.ts"], "names": [], "mappings": "", "sourcesContent": ["import { Options as BadginOptions } from 'badgin';\nimport { ProxyNativeModule } from 'expo-modules-core';\n\nexport type WebSetBadgeCountOptions = BadginOptions;\ntype SetBadgeCountOptions = WebSetBadgeCountOptions | undefined;\n\nexport interface BadgeModule extends ProxyNativeModule {\n  getBadgeCountAsync?: () => Promise<number>;\n  setBadgeCountAsync?: (badgeCount: number, options: SetBadgeCountOptions) => Promise<boolean>;\n}\n"]}