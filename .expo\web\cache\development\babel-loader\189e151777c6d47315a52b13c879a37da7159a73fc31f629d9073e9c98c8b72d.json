{"ast": null, "code": "import _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport _objectWithoutProperties from \"@babel/runtime/helpers/objectWithoutProperties\";\nvar _excluded = [\"children\", \"style\", \"visible\", \"size\"],\n  _excluded2 = [\"backgroundColor\"];\nfunction _extends() {\n  _extends = Object.assign ? Object.assign.bind() : function (target) {\n    for (var i = 1; i < arguments.length; i++) {\n      var source = arguments[i];\n      for (var key in source) {\n        if (Object.prototype.hasOwnProperty.call(source, key)) {\n          target[key] = source[key];\n        }\n      }\n    }\n    return target;\n  };\n  return _extends.apply(this, arguments);\n}\nimport { useTheme } from '@react-navigation/native';\nimport color from 'color';\nimport * as React from 'react';\nimport Animated from \"react-native-web/dist/exports/Animated\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nexport default function Badge(_ref) {\n  var children = _ref.children,\n    style = _ref.style,\n    _ref$visible = _ref.visible,\n    visible = _ref$visible === void 0 ? true : _ref$visible,\n    _ref$size = _ref.size,\n    size = _ref$size === void 0 ? 18 : _ref$size,\n    rest = _objectWithoutProperties(_ref, _excluded);\n  var _React$useState = React.useState(function () {\n      return new Animated.Value(visible ? 1 : 0);\n    }),\n    _React$useState2 = _slicedToArray(_React$useState, 1),\n    opacity = _React$useState2[0];\n  var _React$useState3 = React.useState(visible),\n    _React$useState4 = _slicedToArray(_React$useState3, 2),\n    rendered = _React$useState4[0],\n    setRendered = _React$useState4[1];\n  var theme = useTheme();\n  React.useEffect(function () {\n    if (!rendered) {\n      return;\n    }\n    Animated.timing(opacity, {\n      toValue: visible ? 1 : 0,\n      duration: 150,\n      useNativeDriver: true\n    }).start(function (_ref2) {\n      var finished = _ref2.finished;\n      if (finished && !visible) {\n        setRendered(false);\n      }\n    });\n    return function () {\n      return opacity.stopAnimation();\n    };\n  }, [opacity, rendered, visible]);\n  if (!rendered) {\n    if (visible) {\n      setRendered(true);\n    } else {\n      return null;\n    }\n  }\n  var _ref3 = StyleSheet.flatten(style) || {},\n    _ref3$backgroundColor = _ref3.backgroundColor,\n    backgroundColor = _ref3$backgroundColor === void 0 ? theme.colors.notification : _ref3$backgroundColor,\n    restStyle = _objectWithoutProperties(_ref3, _excluded2);\n  var textColor = color(backgroundColor).isLight() ? 'black' : 'white';\n  var borderRadius = size / 2;\n  var fontSize = Math.floor(size * 3 / 4);\n  return React.createElement(Animated.Text, _extends({\n    numberOfLines: 1,\n    style: [{\n      transform: [{\n        scale: opacity.interpolate({\n          inputRange: [0, 1],\n          outputRange: [0.5, 1]\n        })\n      }],\n      color: textColor,\n      lineHeight: size - 1,\n      height: size,\n      minWidth: size,\n      opacity: opacity,\n      backgroundColor: backgroundColor,\n      fontSize: fontSize,\n      borderRadius: borderRadius\n    }, styles.container, restStyle]\n  }, rest), children);\n}\nvar styles = StyleSheet.create({\n  container: {\n    alignSelf: 'flex-end',\n    textAlign: 'center',\n    paddingHorizontal: 4,\n    overflow: 'hidden'\n  }\n});", "map": {"version": 3, "names": ["useTheme", "color", "React", "Animated", "StyleSheet", "Badge", "_ref", "children", "style", "_ref$visible", "visible", "_ref$size", "size", "rest", "_objectWithoutProperties", "_excluded", "_React$useState", "useState", "Value", "_React$useState2", "_slicedToArray", "opacity", "_React$useState3", "_React$useState4", "rendered", "setRendered", "theme", "useEffect", "timing", "toValue", "duration", "useNativeDriver", "start", "_ref2", "finished", "stopAnimation", "_ref3", "flatten", "_ref3$backgroundColor", "backgroundColor", "colors", "notification", "restStyle", "_excluded2", "textColor", "isLight", "borderRadius", "fontSize", "Math", "floor", "createElement", "Text", "_extends", "numberOfLines", "transform", "scale", "interpolate", "inputRange", "outputRange", "lineHeight", "height", "min<PERSON><PERSON><PERSON>", "styles", "container", "create", "alignSelf", "textAlign", "paddingHorizontal", "overflow"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\@react-navigation\\bottom-tabs\\src\\views\\Badge.tsx"], "sourcesContent": ["import { useTheme } from '@react-navigation/native';\nimport color from 'color';\nimport * as React from 'react';\nimport { Animated, StyleProp, StyleSheet, TextStyle } from 'react-native';\n\ntype Props = {\n  /**\n   * Whether the badge is visible\n   */\n  visible: boolean;\n  /**\n   * Content of the `Badge`.\n   */\n  children?: string | number;\n  /**\n   * Size of the `Badge`.\n   */\n  size?: number;\n  /**\n   * Style object for the tab bar container.\n   */\n  style?: Animated.WithAnimatedValue<StyleProp<TextStyle>>;\n};\n\nexport default function Badge({\n  children,\n  style,\n  visible = true,\n  size = 18,\n  ...rest\n}: Props) {\n  const [opacity] = React.useState(() => new Animated.Value(visible ? 1 : 0));\n  const [rendered, setRendered] = React.useState(visible);\n\n  const theme = useTheme();\n\n  React.useEffect(() => {\n    if (!rendered) {\n      return;\n    }\n\n    Animated.timing(opacity, {\n      toValue: visible ? 1 : 0,\n      duration: 150,\n      useNativeDriver: true,\n    }).start(({ finished }) => {\n      if (finished && !visible) {\n        setRendered(false);\n      }\n    });\n\n    return () => opacity.stopAnimation();\n  }, [opacity, rendered, visible]);\n\n  if (!rendered) {\n    if (visible) {\n      setRendered(true);\n    } else {\n      return null;\n    }\n  }\n\n  // @ts-expect-error: backgroundColor definitely exists\n  const { backgroundColor = theme.colors.notification, ...restStyle } =\n    StyleSheet.flatten(style) || {};\n  const textColor = color(backgroundColor).isLight() ? 'black' : 'white';\n\n  const borderRadius = size / 2;\n  const fontSize = Math.floor((size * 3) / 4);\n\n  return (\n    <Animated.Text\n      numberOfLines={1}\n      style={[\n        {\n          transform: [\n            {\n              scale: opacity.interpolate({\n                inputRange: [0, 1],\n                outputRange: [0.5, 1],\n              }),\n            },\n          ],\n          color: textColor,\n          lineHeight: size - 1,\n          height: size,\n          minWidth: size,\n          opacity,\n          backgroundColor,\n          fontSize,\n          borderRadius,\n        },\n        styles.container,\n        restStyle,\n      ]}\n      {...rest}\n    >\n      {children}\n    </Animated.Text>\n  );\n}\n\nconst styles = StyleSheet.create({\n  container: {\n    alignSelf: 'flex-end',\n    textAlign: 'center',\n    paddingHorizontal: 4,\n    overflow: 'hidden',\n  },\n});\n"], "mappings": ";;;;;;;;;;;;;;;;;;AAAA,SAASA,QAAQ,QAAQ,0BAA0B;AACnD,OAAOC,KAAK,MAAM,OAAO;AACzB,OAAO,KAAKC,KAAK,MAAM,OAAO;AAAA,OAAAC,QAAA;AAAA,OAAAC,UAAA;AAsB9B,eAAe,SAASC,KAAKA,CAAAC,IAAA,EAMnB;EAAA,IALRC,QAAQ,GAKFD,IAAA,CALNC,QAAQ;IACRC,KAAK,GAICF,IAAA,CAJNE,KAAK;IAAAC,YAAA,GAICH,IAAA,CAHNI,OAAO;IAAPA,OAAO,GAAAD,YAAA,cAAG,IAAI,GAAAA,YAAA;IAAAE,SAAA,GAGRL,IAAA,CAFNM,IAAI;IAAJA,IAAI,GAAAD,SAAA,cAAG,EAAE,GAAAA,SAAA;IACNE,IAAA,GAAAC,wBAAA,CACGR,IAAA,EAAAS,SAAA;EACN,IAAAC,eAAA,GAAkBd,KAAK,CAACe,QAAQ,CAAC;MAAA,OAAM,IAAId,QAAQ,CAACe,KAAK,CAACR,OAAO,GAAG,CAAC,GAAG,CAAC,CAAC;IAAA,EAAC;IAAAS,gBAAA,GAAAC,cAAA,CAAAJ,eAAA;IAApEK,OAAO,GAAAF,gBAAA;EACd,IAAAG,gBAAA,GAAgCpB,KAAK,CAACe,QAAQ,CAACP,OAAO,CAAC;IAAAa,gBAAA,GAAAH,cAAA,CAAAE,gBAAA;IAAhDE,QAAQ,GAAAD,gBAAA;IAAEE,WAAW,GAAAF,gBAAA;EAE5B,IAAMG,KAAK,GAAG1B,QAAQ,EAAE;EAExBE,KAAK,CAACyB,SAAS,CAAC,YAAM;IACpB,IAAI,CAACH,QAAQ,EAAE;MACb;IACF;IAEArB,QAAQ,CAACyB,MAAM,CAACP,OAAO,EAAE;MACvBQ,OAAO,EAAEnB,OAAO,GAAG,CAAC,GAAG,CAAC;MACxBoB,QAAQ,EAAE,GAAG;MACbC,eAAe,EAAE;IACnB,CAAC,CAAC,CAACC,KAAK,CAAC,UAAAC,KAAA,EAAkB;MAAA,IAAfC,QAAA,GAAUD,KAAA,CAAVC,QAAA;MACV,IAAIA,QAAQ,IAAI,CAACxB,OAAO,EAAE;QACxBe,WAAW,CAAC,KAAK,CAAC;MACpB;IACF,CAAC,CAAC;IAEF,OAAO;MAAA,OAAMJ,OAAO,CAACc,aAAa,EAAE;IAAA;EACtC,CAAC,EAAE,CAACd,OAAO,EAAEG,QAAQ,EAAEd,OAAO,CAAC,CAAC;EAEhC,IAAI,CAACc,QAAQ,EAAE;IACb,IAAId,OAAO,EAAE;MACXe,WAAW,CAAC,IAAI,CAAC;IACnB,CAAC,MAAM;MACL,OAAO,IAAI;IACb;EACF;EAGA,IAAAW,KAAA,GACEhC,UAAU,CAACiC,OAAO,CAAC7B,KAAK,CAAC,IAAI,CAAC,CAAC;IAAA8B,qBAAA,GAAAF,KAAA,CADzBG,eAAe;IAAfA,eAAe,GAAAD,qBAAA,cAAGZ,KAAK,CAACc,MAAM,CAACC,YAAY,GAAAH,qBAAA;IAAKI,SAAA,GAAA5B,wBAAA,CAAAsB,KAAA,EAAAO,UAAA;EAExD,IAAMC,SAAS,GAAG3C,KAAK,CAACsC,eAAe,CAAC,CAACM,OAAO,EAAE,GAAG,OAAO,GAAG,OAAO;EAEtE,IAAMC,YAAY,GAAGlC,IAAI,GAAG,CAAC;EAC7B,IAAMmC,QAAQ,GAAGC,IAAI,CAACC,KAAK,CAAErC,IAAI,GAAG,CAAC,GAAI,CAAC,CAAC;EAE3C,OACEV,KAAA,CAAAgD,aAAA,CAAC/C,QAAQ,CAACgD,IAAI,EAAAC,QAAA;IACZC,aAAa,EAAE,CAAE;IACjB7C,KAAK,EAAE,CACL;MACE8C,SAAS,EAAE,CACT;QACEC,KAAK,EAAElC,OAAO,CAACmC,WAAW,CAAC;UACzBC,UAAU,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;UAClBC,WAAW,EAAE,CAAC,GAAG,EAAE,CAAC;QACtB,CAAC;MACH,CAAC,CACF;MACDzD,KAAK,EAAE2C,SAAS;MAChBe,UAAU,EAAE/C,IAAI,GAAG,CAAC;MACpBgD,MAAM,EAAEhD,IAAI;MACZiD,QAAQ,EAAEjD,IAAI;MACdS,OAAO,EAAPA,OAAO;MACPkB,eAAe,EAAfA,eAAe;MACfQ,QAAQ,EAARA,QAAQ;MACRD,YAAA,EAAAA;IACF,CAAC,EACDgB,MAAM,CAACC,SAAS,EAChBrB,SAAS;EACT,GACE7B,IAAI,GAEPN,QAAQ,CACK;AAEpB;AAEA,IAAMuD,MAAM,GAAG1D,UAAU,CAAC4D,MAAM,CAAC;EAC/BD,SAAS,EAAE;IACTE,SAAS,EAAE,UAAU;IACrBC,SAAS,EAAE,QAAQ;IACnBC,iBAAiB,EAAE,CAAC;IACpBC,QAAQ,EAAE;EACZ;AACF,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}