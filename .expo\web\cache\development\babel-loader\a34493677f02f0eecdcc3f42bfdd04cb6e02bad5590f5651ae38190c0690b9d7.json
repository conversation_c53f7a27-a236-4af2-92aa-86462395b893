{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useState } from 'react';\nimport View from \"react-native-web/dist/exports/View\";\nimport StyleSheet from \"react-native-web/dist/exports/StyleSheet\";\nimport ScrollView from \"react-native-web/dist/exports/ScrollView\";\nimport RefreshControl from \"react-native-web/dist/exports/RefreshControl\";\nimport { Text, Card, Chip, FAB, Searchbar, SegmentedButtons, useTheme } from 'react-native-paper';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { MaterialIcons } from '@expo/vector-icons';\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar mockProducts = [{\n  id: '1',\n  name: 'حليب نادك كامل الدسم',\n  barcode: '123456789',\n  expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n  status: 'warning',\n  location: 'fridge',\n  quantity: 2,\n  category: 'dairy',\n  addedAt: new Date(),\n  brand: 'نادك'\n}, {\n  id: '2',\n  name: 'خبز التميس الأبيض',\n  barcode: '987654321',\n  expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n  status: 'fresh',\n  location: 'counter',\n  quantity: 1,\n  category: 'bakery',\n  addedAt: new Date(),\n  brand: 'التميس'\n}, {\n  id: '3',\n  name: 'زبادي المراعي بالفراولة',\n  barcode: '456789123',\n  expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n  status: 'expired',\n  location: 'fridge',\n  quantity: 3,\n  category: 'dairy',\n  addedAt: new Date(),\n  brand: 'المراعي'\n}, {\n  id: '4',\n  name: 'أرز بسمتي',\n  barcode: '789123456',\n  expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),\n  status: 'fresh',\n  location: 'pantry',\n  quantity: 1,\n  category: 'grains',\n  addedAt: new Date(),\n  brand: 'الأرز الذهبي'\n}, {\n  id: '5',\n  name: 'عصير برتقال طبيعي',\n  barcode: '321654987',\n  expiryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),\n  status: 'fresh',\n  location: 'fridge',\n  quantity: 2,\n  category: 'beverages',\n  addedAt: new Date(),\n  brand: 'العصائر الطبيعية'\n}];\nvar EnhancedProductsScreen = function EnhancedProductsScreen() {\n  var theme = useTheme();\n  var _useState = useState(''),\n    _useState2 = _slicedToArray(_useState, 2),\n    searchQuery = _useState2[0],\n    setSearchQuery = _useState2[1];\n  var _useState3 = useState('all'),\n    _useState4 = _slicedToArray(_useState3, 2),\n    selectedFilter = _useState4[0],\n    setSelectedFilter = _useState4[1];\n  var _useState5 = useState(false),\n    _useState6 = _slicedToArray(_useState5, 2),\n    refreshing = _useState6[0],\n    setRefreshing = _useState6[1];\n  var filterOptions = [{\n    value: 'all',\n    label: 'الكل'\n  }, {\n    value: 'fresh',\n    label: 'طازج'\n  }, {\n    value: 'warning',\n    label: 'تحذير'\n  }, {\n    value: 'expired',\n    label: 'منتهي'\n  }];\n  var getStatusColor = function getStatusColor(status) {\n    switch (status) {\n      case 'fresh':\n        return '#4CAF50';\n      case 'warning':\n        return '#FF9800';\n      case 'expired':\n        return '#F44336';\n      default:\n        return theme.colors.primary;\n    }\n  };\n  var getLocationIcon = function getLocationIcon(location) {\n    switch (location) {\n      case 'fridge':\n        return 'kitchen';\n      case 'freezer':\n        return 'ac-unit';\n      case 'pantry':\n        return 'store';\n      case 'counter':\n        return 'countertops';\n      default:\n        return 'inventory';\n    }\n  };\n  var getLocationName = function getLocationName(location) {\n    switch (location) {\n      case 'fridge':\n        return 'الثلاجة';\n      case 'freezer':\n        return 'الفريزر';\n      case 'pantry':\n        return 'المخزن';\n      case 'counter':\n        return 'المنضدة';\n      case 'cabinet':\n        return 'الخزانة';\n      default:\n        return 'أخرى';\n    }\n  };\n  var getStatusName = function getStatusName(status) {\n    switch (status) {\n      case 'fresh':\n        return 'طازج';\n      case 'warning':\n        return 'تحذير';\n      case 'expired':\n        return 'منتهي الصلاحية';\n      default:\n        return status;\n    }\n  };\n  var getDaysUntilExpiry = function getDaysUntilExpiry(expiryDate) {\n    var today = new Date();\n    var diffTime = expiryDate.getTime() - today.getTime();\n    var diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n  var filteredProducts = mockProducts.filter(function (product) {\n    var matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) || product.brand.toLowerCase().includes(searchQuery.toLowerCase());\n    var matchesFilter = selectedFilter === 'all' || product.status === selectedFilter;\n    return matchesSearch && matchesFilter;\n  });\n  var onRefresh = function () {\n    var _ref = _asyncToGenerator(function* () {\n      setRefreshing(true);\n      yield new Promise(function (resolve) {\n        return setTimeout(resolve, 1000);\n      });\n      setRefreshing(false);\n    });\n    return function onRefresh() {\n      return _ref.apply(this, arguments);\n    };\n  }();\n  return _jsxs(SafeAreaView, {\n    style: styles.container,\n    children: [_jsxs(View, {\n      style: styles.header,\n      children: [_jsx(Searchbar, {\n        placeholder: \"\\u0627\\u0644\\u0628\\u062D\\u062B \\u0641\\u064A \\u0627\\u0644\\u0645\\u0646\\u062A\\u062C\\u0627\\u062A...\",\n        onChangeText: setSearchQuery,\n        value: searchQuery,\n        style: styles.searchbar\n      }), _jsx(SegmentedButtons, {\n        value: selectedFilter,\n        onValueChange: setSelectedFilter,\n        buttons: filterOptions,\n        style: styles.filterButtons\n      })]\n    }), _jsxs(ScrollView, {\n      style: styles.scrollView,\n      refreshControl: _jsx(RefreshControl, {\n        refreshing: refreshing,\n        onRefresh: onRefresh\n      }),\n      children: [_jsxs(Text, {\n        style: styles.resultsText,\n        children: [filteredProducts.length, \" \\u0645\\u0646\\u062A\\u062C\"]\n      }), filteredProducts.map(function (product) {\n        var daysUntilExpiry = getDaysUntilExpiry(product.expiryDate);\n        return _jsx(Card, {\n          style: styles.productCard,\n          children: _jsxs(Card.Content, {\n            children: [_jsxs(View, {\n              style: styles.productHeader,\n              children: [_jsxs(View, {\n                style: styles.productInfo,\n                children: [_jsx(Text, {\n                  style: styles.productName,\n                  children: product.name\n                }), _jsx(Text, {\n                  style: styles.productBrand,\n                  children: product.brand\n                }), _jsxs(View, {\n                  style: styles.productMeta,\n                  children: [_jsxs(View, {\n                    style: styles.metaItem,\n                    children: [_jsx(MaterialIcons, {\n                      name: getLocationIcon(product.location),\n                      size: 16,\n                      color: theme.colors.onSurfaceVariant\n                    }), _jsx(Text, {\n                      style: styles.metaText,\n                      children: getLocationName(product.location)\n                    })]\n                  }), _jsxs(View, {\n                    style: styles.metaItem,\n                    children: [_jsx(MaterialIcons, {\n                      name: \"inventory\",\n                      size: 16,\n                      color: theme.colors.onSurfaceVariant\n                    }), _jsxs(Text, {\n                      style: styles.metaText,\n                      children: [\"\\u0627\\u0644\\u0643\\u0645\\u064A\\u0629: \", product.quantity]\n                    })]\n                  })]\n                })]\n              }), _jsx(Chip, {\n                style: [styles.statusChip, {\n                  backgroundColor: getStatusColor(product.status) + '20'\n                }],\n                textStyle: {\n                  color: getStatusColor(product.status),\n                  fontSize: 12\n                },\n                children: getStatusName(product.status)\n              })]\n            }), _jsxs(View, {\n              style: styles.expiryInfo,\n              children: [_jsxs(Text, {\n                style: styles.expiryDate,\n                children: [\"\\u0627\\u0646\\u062A\\u0647\\u0627\\u0621 \\u0627\\u0644\\u0635\\u0644\\u0627\\u062D\\u064A\\u0629: \", product.expiryDate.toLocaleDateString('ar-SA')]\n              }), _jsx(Text, {\n                style: [styles.daysRemaining, {\n                  color: getStatusColor(product.status)\n                }],\n                children: daysUntilExpiry > 0 ? `${daysUntilExpiry} يوم متبقي` : daysUntilExpiry === 0 ? 'ينتهي اليوم' : `منتهي منذ ${Math.abs(daysUntilExpiry)} يوم`\n              })]\n            })]\n          })\n        }, product.id);\n      }), filteredProducts.length === 0 && _jsxs(View, {\n        style: styles.emptyState,\n        children: [_jsx(MaterialIcons, {\n          name: \"inventory\",\n          size: 64,\n          color: theme.colors.onSurfaceVariant\n        }), _jsx(Text, {\n          style: styles.emptyText,\n          children: \"\\u0644\\u0627 \\u062A\\u0648\\u062C\\u062F \\u0645\\u0646\\u062A\\u062C\\u0627\\u062A\"\n        }), _jsx(Text, {\n          style: styles.emptySubtext,\n          children: searchQuery ? 'لم يتم العثور على منتجات تطابق البحث' : 'ابدأ بإضافة منتجاتك الأولى'\n        })]\n      })]\n    }), _jsx(FAB, {\n      style: styles.fab,\n      icon: \"add\",\n      onPress: function onPress() {}\n    })]\n  });\n};\nvar styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5'\n  },\n  header: {\n    padding: 16,\n    backgroundColor: 'white',\n    elevation: 2\n  },\n  searchbar: {\n    marginBottom: 12\n  },\n  filterButtons: {\n    marginBottom: 8\n  },\n  scrollView: {\n    flex: 1\n  },\n  resultsText: {\n    margin: 16,\n    fontSize: 14,\n    opacity: 0.7\n  },\n  productCard: {\n    marginHorizontal: 16,\n    marginBottom: 8,\n    borderRadius: 8\n  },\n  productHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start'\n  },\n  productInfo: {\n    flex: 1\n  },\n  productName: {\n    fontSize: 16,\n    fontWeight: 'bold'\n  },\n  productBrand: {\n    fontSize: 14,\n    opacity: 0.7,\n    marginTop: 2\n  },\n  productMeta: {\n    flexDirection: 'row',\n    marginTop: 8,\n    gap: 16\n  },\n  metaItem: {\n    flexDirection: 'row',\n    alignItems: 'center'\n  },\n  metaText: {\n    marginLeft: 4,\n    fontSize: 12,\n    opacity: 0.7\n  },\n  statusChip: {\n    marginLeft: 8\n  },\n  expiryInfo: {\n    marginTop: 12,\n    paddingTop: 12,\n    borderTopWidth: 1,\n    borderTopColor: '#f0f0f0'\n  },\n  expiryDate: {\n    fontSize: 12,\n    opacity: 0.7\n  },\n  daysRemaining: {\n    fontSize: 12,\n    fontWeight: 'bold',\n    marginTop: 2\n  },\n  emptyState: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: 40,\n    marginTop: 60\n  },\n  emptyText: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginTop: 16\n  },\n  emptySubtext: {\n    fontSize: 14,\n    opacity: 0.7,\n    textAlign: 'center',\n    marginTop: 8\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0\n  }\n});\nexport default EnhancedProductsScreen;", "map": {"version": 3, "names": ["React", "useState", "View", "StyleSheet", "ScrollView", "RefreshControl", "Text", "Card", "Chip", "FAB", "Searchbar", "SegmentedButtons", "useTheme", "SafeAreaView", "MaterialIcons", "jsx", "_jsx", "jsxs", "_jsxs", "mockProducts", "id", "name", "barcode", "expiryDate", "Date", "now", "status", "location", "quantity", "category", "addedAt", "brand", "EnhancedProductsScreen", "theme", "_useState", "_useState2", "_slicedToArray", "searchQuery", "setSearch<PERSON>uery", "_useState3", "_useState4", "<PERSON><PERSON><PERSON><PERSON>", "setSelectedFilter", "_useState5", "_useState6", "refreshing", "setRefreshing", "filterOptions", "value", "label", "getStatusColor", "colors", "primary", "getLocationIcon", "getLocationName", "getStatusName", "getDaysUntilExpiry", "today", "diffTime", "getTime", "diffDays", "Math", "ceil", "filteredProducts", "filter", "product", "matchesSearch", "toLowerCase", "includes", "matchesFilter", "onRefresh", "_ref", "_asyncToGenerator", "Promise", "resolve", "setTimeout", "apply", "arguments", "style", "styles", "container", "children", "header", "placeholder", "onChangeText", "searchbar", "onValueChange", "buttons", "filterButtons", "scrollView", "refreshControl", "resultsText", "length", "map", "daysUntilExpiry", "productCard", "Content", "productHeader", "productInfo", "productName", "productBrand", "productMeta", "metaItem", "size", "color", "onSurfaceVariant", "metaText", "statusChip", "backgroundColor", "textStyle", "fontSize", "expiryInfo", "toLocaleDateString", "daysRemaining", "abs", "emptyState", "emptyText", "emptySubtext", "fab", "icon", "onPress", "create", "flex", "padding", "elevation", "marginBottom", "margin", "opacity", "marginHorizontal", "borderRadius", "flexDirection", "justifyContent", "alignItems", "fontWeight", "marginTop", "gap", "marginLeft", "paddingTop", "borderTopWidth", "borderTopColor", "textAlign", "position", "right", "bottom"], "sources": ["C:/Users/<USER>/Music/mobile/src/screens/EnhancedProductsScreen.tsx"], "sourcesContent": ["import React, { useState } from 'react';\nimport {\n  View,\n  StyleSheet,\n  ScrollView,\n  RefreshControl,\n} from 'react-native';\nimport {\n  Text,\n  Card,\n  Chip,\n  FAB,\n  Searchbar,\n  SegmentedButtons,\n  useTheme,\n} from 'react-native-paper';\nimport { SafeAreaView } from 'react-native-safe-area-context';\nimport { MaterialIcons } from '@expo/vector-icons';\n\n// بيانات وهمية موسعة\nconst mockProducts = [\n  {\n    id: '1',\n    name: 'حليب نادك كامل الدسم',\n    barcode: '123456789',\n    expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000),\n    status: 'warning' as const,\n    location: 'fridge' as const,\n    quantity: 2,\n    category: 'dairy',\n    addedAt: new Date(),\n    brand: 'نادك',\n  },\n  {\n    id: '2',\n    name: 'خبز التميس الأبيض',\n    barcode: '987654321',\n    expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000),\n    status: 'fresh' as const,\n    location: 'counter' as const,\n    quantity: 1,\n    category: 'bakery',\n    addedAt: new Date(),\n    brand: 'التميس',\n  },\n  {\n    id: '3',\n    name: 'زبادي المراعي بالفراولة',\n    barcode: '456789123',\n    expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000),\n    status: 'expired' as const,\n    location: 'fridge' as const,\n    quantity: 3,\n    category: 'dairy',\n    addedAt: new Date(),\n    brand: 'المراعي',\n  },\n  {\n    id: '4',\n    name: 'أرز بسمتي',\n    barcode: '789123456',\n    expiryDate: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000),\n    status: 'fresh' as const,\n    location: 'pantry' as const,\n    quantity: 1,\n    category: 'grains',\n    addedAt: new Date(),\n    brand: 'الأرز الذهبي',\n  },\n  {\n    id: '5',\n    name: 'عصير برتقال طبيعي',\n    barcode: '321654987',\n    expiryDate: new Date(Date.now() + 5 * 24 * 60 * 60 * 1000),\n    status: 'fresh' as const,\n    location: 'fridge' as const,\n    quantity: 2,\n    category: 'beverages',\n    addedAt: new Date(),\n    brand: 'العصائر الطبيعية',\n  },\n];\n\nconst EnhancedProductsScreen: React.FC = () => {\n  const theme = useTheme();\n  const [searchQuery, setSearchQuery] = useState('');\n  const [selectedFilter, setSelectedFilter] = useState('all');\n  const [refreshing, setRefreshing] = useState(false);\n\n  const filterOptions = [\n    { value: 'all', label: 'الكل' },\n    { value: 'fresh', label: 'طازج' },\n    { value: 'warning', label: 'تحذير' },\n    { value: 'expired', label: 'منتهي' },\n  ];\n\n  const getStatusColor = (status: string) => {\n    switch (status) {\n      case 'fresh': return '#4CAF50';\n      case 'warning': return '#FF9800';\n      case 'expired': return '#F44336';\n      default: return theme.colors.primary;\n    }\n  };\n\n  const getLocationIcon = (location: string) => {\n    switch (location) {\n      case 'fridge': return 'kitchen';\n      case 'freezer': return 'ac-unit';\n      case 'pantry': return 'store';\n      case 'counter': return 'countertops';\n      default: return 'inventory';\n    }\n  };\n\n  const getLocationName = (location: string) => {\n    switch (location) {\n      case 'fridge': return 'الثلاجة';\n      case 'freezer': return 'الفريزر';\n      case 'pantry': return 'المخزن';\n      case 'counter': return 'المنضدة';\n      case 'cabinet': return 'الخزانة';\n      default: return 'أخرى';\n    }\n  };\n\n  const getStatusName = (status: string) => {\n    switch (status) {\n      case 'fresh': return 'طازج';\n      case 'warning': return 'تحذير';\n      case 'expired': return 'منتهي الصلاحية';\n      default: return status;\n    }\n  };\n\n  const getDaysUntilExpiry = (expiryDate: Date) => {\n    const today = new Date();\n    const diffTime = expiryDate.getTime() - today.getTime();\n    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));\n    return diffDays;\n  };\n\n  const filteredProducts = mockProducts.filter(product => {\n    const matchesSearch = product.name.toLowerCase().includes(searchQuery.toLowerCase()) ||\n                         product.brand.toLowerCase().includes(searchQuery.toLowerCase());\n    const matchesFilter = selectedFilter === 'all' || product.status === selectedFilter;\n    return matchesSearch && matchesFilter;\n  });\n\n  const onRefresh = async () => {\n    setRefreshing(true);\n    await new Promise(resolve => setTimeout(resolve, 1000));\n    setRefreshing(false);\n  };\n\n  return (\n    <SafeAreaView style={styles.container}>\n      <View style={styles.header}>\n        <Searchbar\n          placeholder=\"البحث في المنتجات...\"\n          onChangeText={setSearchQuery}\n          value={searchQuery}\n          style={styles.searchbar}\n        />\n        \n        <SegmentedButtons\n          value={selectedFilter}\n          onValueChange={setSelectedFilter}\n          buttons={filterOptions}\n          style={styles.filterButtons}\n        />\n      </View>\n\n      <ScrollView\n        style={styles.scrollView}\n        refreshControl={\n          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />\n        }\n      >\n        <Text style={styles.resultsText}>\n          {filteredProducts.length} منتج\n        </Text>\n\n        {filteredProducts.map((product) => {\n          const daysUntilExpiry = getDaysUntilExpiry(product.expiryDate);\n          \n          return (\n            <Card key={product.id} style={styles.productCard}>\n              <Card.Content>\n                <View style={styles.productHeader}>\n                  <View style={styles.productInfo}>\n                    <Text style={styles.productName}>{product.name}</Text>\n                    <Text style={styles.productBrand}>{product.brand}</Text>\n                    \n                    <View style={styles.productMeta}>\n                      <View style={styles.metaItem}>\n                        <MaterialIcons \n                          name={getLocationIcon(product.location)} \n                          size={16} \n                          color={theme.colors.onSurfaceVariant} \n                        />\n                        <Text style={styles.metaText}>\n                          {getLocationName(product.location)}\n                        </Text>\n                      </View>\n                      \n                      <View style={styles.metaItem}>\n                        <MaterialIcons \n                          name=\"inventory\" \n                          size={16} \n                          color={theme.colors.onSurfaceVariant} \n                        />\n                        <Text style={styles.metaText}>\n                          الكمية: {product.quantity}\n                        </Text>\n                      </View>\n                    </View>\n                  </View>\n                  \n                  <Chip \n                    style={[styles.statusChip, { backgroundColor: getStatusColor(product.status) + '20' }]}\n                    textStyle={{ color: getStatusColor(product.status), fontSize: 12 }}\n                  >\n                    {getStatusName(product.status)}\n                  </Chip>\n                </View>\n                \n                <View style={styles.expiryInfo}>\n                  <Text style={styles.expiryDate}>\n                    انتهاء الصلاحية: {product.expiryDate.toLocaleDateString('ar-SA')}\n                  </Text>\n                  <Text style={[\n                    styles.daysRemaining,\n                    { color: getStatusColor(product.status) }\n                  ]}>\n                    {daysUntilExpiry > 0 \n                      ? `${daysUntilExpiry} يوم متبقي`\n                      : daysUntilExpiry === 0 \n                        ? 'ينتهي اليوم'\n                        : `منتهي منذ ${Math.abs(daysUntilExpiry)} يوم`\n                    }\n                  </Text>\n                </View>\n              </Card.Content>\n            </Card>\n          );\n        })}\n\n        {filteredProducts.length === 0 && (\n          <View style={styles.emptyState}>\n            <MaterialIcons name=\"inventory\" size={64} color={theme.colors.onSurfaceVariant} />\n            <Text style={styles.emptyText}>لا توجد منتجات</Text>\n            <Text style={styles.emptySubtext}>\n              {searchQuery ? 'لم يتم العثور على منتجات تطابق البحث' : 'ابدأ بإضافة منتجاتك الأولى'}\n            </Text>\n          </View>\n        )}\n      </ScrollView>\n\n      <FAB\n        style={styles.fab}\n        icon=\"add\"\n        onPress={() => {/* navigation.navigate('AddProduct') */}}\n      />\n    </SafeAreaView>\n  );\n};\n\nconst styles = StyleSheet.create({\n  container: {\n    flex: 1,\n    backgroundColor: '#f5f5f5',\n  },\n  header: {\n    padding: 16,\n    backgroundColor: 'white',\n    elevation: 2,\n  },\n  searchbar: {\n    marginBottom: 12,\n  },\n  filterButtons: {\n    marginBottom: 8,\n  },\n  scrollView: {\n    flex: 1,\n  },\n  resultsText: {\n    margin: 16,\n    fontSize: 14,\n    opacity: 0.7,\n  },\n  productCard: {\n    marginHorizontal: 16,\n    marginBottom: 8,\n    borderRadius: 8,\n  },\n  productHeader: {\n    flexDirection: 'row',\n    justifyContent: 'space-between',\n    alignItems: 'flex-start',\n  },\n  productInfo: {\n    flex: 1,\n  },\n  productName: {\n    fontSize: 16,\n    fontWeight: 'bold',\n  },\n  productBrand: {\n    fontSize: 14,\n    opacity: 0.7,\n    marginTop: 2,\n  },\n  productMeta: {\n    flexDirection: 'row',\n    marginTop: 8,\n    gap: 16,\n  },\n  metaItem: {\n    flexDirection: 'row',\n    alignItems: 'center',\n  },\n  metaText: {\n    marginLeft: 4,\n    fontSize: 12,\n    opacity: 0.7,\n  },\n  statusChip: {\n    marginLeft: 8,\n  },\n  expiryInfo: {\n    marginTop: 12,\n    paddingTop: 12,\n    borderTopWidth: 1,\n    borderTopColor: '#f0f0f0',\n  },\n  expiryDate: {\n    fontSize: 12,\n    opacity: 0.7,\n  },\n  daysRemaining: {\n    fontSize: 12,\n    fontWeight: 'bold',\n    marginTop: 2,\n  },\n  emptyState: {\n    alignItems: 'center',\n    justifyContent: 'center',\n    padding: 40,\n    marginTop: 60,\n  },\n  emptyText: {\n    fontSize: 18,\n    fontWeight: 'bold',\n    marginTop: 16,\n  },\n  emptySubtext: {\n    fontSize: 14,\n    opacity: 0.7,\n    textAlign: 'center',\n    marginTop: 8,\n  },\n  fab: {\n    position: 'absolute',\n    margin: 16,\n    right: 0,\n    bottom: 0,\n  },\n});\n\nexport default EnhancedProductsScreen;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,QAAQ,QAAQ,OAAO;AAAC,OAAAC,IAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,UAAA;AAAA,OAAAC,cAAA;AAOxC,SACEC,IAAI,EACJC,IAAI,EACJC,IAAI,EACJC,GAAG,EACHC,SAAS,EACTC,gBAAgB,EAChBC,QAAQ,QACH,oBAAoB;AAC3B,SAASC,YAAY,QAAQ,gCAAgC;AAC7D,SAASC,aAAa,QAAQ,oBAAoB;AAAC,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAGnD,IAAMC,YAAY,GAAG,CACnB;EACEC,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,sBAAsB;EAC5BC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,SAAkB;EAC1BC,QAAQ,EAAE,QAAiB;EAC3BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,IAAIN,IAAI,CAAC,CAAC;EACnBO,KAAK,EAAE;AACT,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,mBAAmB;EACzBC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,OAAgB;EACxBC,QAAQ,EAAE,SAAkB;EAC5BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,IAAIN,IAAI,CAAC,CAAC;EACnBO,KAAK,EAAE;AACT,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,yBAAyB;EAC/BC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,SAAkB;EAC1BC,QAAQ,EAAE,QAAiB;EAC3BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,OAAO;EACjBC,OAAO,EAAE,IAAIN,IAAI,CAAC,CAAC;EACnBO,KAAK,EAAE;AACT,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,WAAW;EACjBC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC5DC,MAAM,EAAE,OAAgB;EACxBC,QAAQ,EAAE,QAAiB;EAC3BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,QAAQ;EAClBC,OAAO,EAAE,IAAIN,IAAI,CAAC,CAAC;EACnBO,KAAK,EAAE;AACT,CAAC,EACD;EACEX,EAAE,EAAE,GAAG;EACPC,IAAI,EAAE,mBAAmB;EACzBC,OAAO,EAAE,WAAW;EACpBC,UAAU,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;EAC1DC,MAAM,EAAE,OAAgB;EACxBC,QAAQ,EAAE,QAAiB;EAC3BC,QAAQ,EAAE,CAAC;EACXC,QAAQ,EAAE,WAAW;EACrBC,OAAO,EAAE,IAAIN,IAAI,CAAC,CAAC;EACnBO,KAAK,EAAE;AACT,CAAC,CACF;AAED,IAAMC,sBAAgC,GAAG,SAAnCA,sBAAgCA,CAAA,EAAS;EAC7C,IAAMC,KAAK,GAAGrB,QAAQ,CAAC,CAAC;EACxB,IAAAsB,SAAA,GAAsCjC,QAAQ,CAAC,EAAE,CAAC;IAAAkC,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA3CG,WAAW,GAAAF,UAAA;IAAEG,cAAc,GAAAH,UAAA;EAClC,IAAAI,UAAA,GAA4CtC,QAAQ,CAAC,KAAK,CAAC;IAAAuC,UAAA,GAAAJ,cAAA,CAAAG,UAAA;IAApDE,cAAc,GAAAD,UAAA;IAAEE,iBAAiB,GAAAF,UAAA;EACxC,IAAAG,UAAA,GAAoC1C,QAAQ,CAAC,KAAK,CAAC;IAAA2C,UAAA,GAAAR,cAAA,CAAAO,UAAA;IAA5CE,UAAU,GAAAD,UAAA;IAAEE,aAAa,GAAAF,UAAA;EAEhC,IAAMG,aAAa,GAAG,CACpB;IAAEC,KAAK,EAAE,KAAK;IAAEC,KAAK,EAAE;EAAO,CAAC,EAC/B;IAAED,KAAK,EAAE,OAAO;IAAEC,KAAK,EAAE;EAAO,CAAC,EACjC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAQ,CAAC,EACpC;IAAED,KAAK,EAAE,SAAS;IAAEC,KAAK,EAAE;EAAQ,CAAC,CACrC;EAED,IAAMC,cAAc,GAAG,SAAjBA,cAAcA,CAAIxB,MAAc,EAAK;IACzC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,SAAS;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAOO,KAAK,CAACkB,MAAM,CAACC,OAAO;IACtC;EACF,CAAC;EAED,IAAMC,eAAe,GAAG,SAAlBA,eAAeA,CAAI1B,QAAgB,EAAK;IAC5C,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,OAAO;MAC7B,KAAK,SAAS;QAAE,OAAO,aAAa;MACpC;QAAS,OAAO,WAAW;IAC7B;EACF,CAAC;EAED,IAAM2B,eAAe,GAAG,SAAlBA,eAAeA,CAAI3B,QAAgB,EAAK;IAC5C,QAAQA,QAAQ;MACd,KAAK,QAAQ;QAAE,OAAO,SAAS;MAC/B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,QAAQ;QAAE,OAAO,QAAQ;MAC9B,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC,KAAK,SAAS;QAAE,OAAO,SAAS;MAChC;QAAS,OAAO,MAAM;IACxB;EACF,CAAC;EAED,IAAM4B,aAAa,GAAG,SAAhBA,aAAaA,CAAI7B,MAAc,EAAK;IACxC,QAAQA,MAAM;MACZ,KAAK,OAAO;QAAE,OAAO,MAAM;MAC3B,KAAK,SAAS;QAAE,OAAO,OAAO;MAC9B,KAAK,SAAS;QAAE,OAAO,gBAAgB;MACvC;QAAS,OAAOA,MAAM;IACxB;EACF,CAAC;EAED,IAAM8B,kBAAkB,GAAG,SAArBA,kBAAkBA,CAAIjC,UAAgB,EAAK;IAC/C,IAAMkC,KAAK,GAAG,IAAIjC,IAAI,CAAC,CAAC;IACxB,IAAMkC,QAAQ,GAAGnC,UAAU,CAACoC,OAAO,CAAC,CAAC,GAAGF,KAAK,CAACE,OAAO,CAAC,CAAC;IACvD,IAAMC,QAAQ,GAAGC,IAAI,CAACC,IAAI,CAACJ,QAAQ,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAC5D,OAAOE,QAAQ;EACjB,CAAC;EAED,IAAMG,gBAAgB,GAAG5C,YAAY,CAAC6C,MAAM,CAAC,UAAAC,OAAO,EAAI;IACtD,IAAMC,aAAa,GAAGD,OAAO,CAAC5C,IAAI,CAAC8C,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,WAAW,CAAC8B,WAAW,CAAC,CAAC,CAAC,IAC/DF,OAAO,CAAClC,KAAK,CAACoC,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC/B,WAAW,CAAC8B,WAAW,CAAC,CAAC,CAAC;IACpF,IAAME,aAAa,GAAG5B,cAAc,KAAK,KAAK,IAAIwB,OAAO,CAACvC,MAAM,KAAKe,cAAc;IACnF,OAAOyB,aAAa,IAAIG,aAAa;EACvC,CAAC,CAAC;EAEF,IAAMC,SAAS;IAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAG,aAAY;MAC5B1B,aAAa,CAAC,IAAI,CAAC;MACnB,MAAM,IAAI2B,OAAO,CAAC,UAAAC,OAAO;QAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;MAAA,EAAC;MACvD5B,aAAa,CAAC,KAAK,CAAC;IACtB,CAAC;IAAA,gBAJKwB,SAASA,CAAA;MAAA,OAAAC,IAAA,CAAAK,KAAA,OAAAC,SAAA;IAAA;EAAA,GAId;EAED,OACE3D,KAAA,CAACL,YAAY;IAACiE,KAAK,EAAEC,MAAM,CAACC,SAAU;IAAAC,QAAA,GACpC/D,KAAA,CAAChB,IAAI;MAAC4E,KAAK,EAAEC,MAAM,CAACG,MAAO;MAAAD,QAAA,GACzBjE,IAAA,CAACN,SAAS;QACRyE,WAAW,EAAC,iGAAsB;QAClCC,YAAY,EAAE9C,cAAe;QAC7BU,KAAK,EAAEX,WAAY;QACnByC,KAAK,EAAEC,MAAM,CAACM;MAAU,CACzB,CAAC,EAEFrE,IAAA,CAACL,gBAAgB;QACfqC,KAAK,EAAEP,cAAe;QACtB6C,aAAa,EAAE5C,iBAAkB;QACjC6C,OAAO,EAAExC,aAAc;QACvB+B,KAAK,EAAEC,MAAM,CAACS;MAAc,CAC7B,CAAC;IAAA,CACE,CAAC,EAEPtE,KAAA,CAACd,UAAU;MACT0E,KAAK,EAAEC,MAAM,CAACU,UAAW;MACzBC,cAAc,EACZ1E,IAAA,CAACX,cAAc;QAACwC,UAAU,EAAEA,UAAW;QAACyB,SAAS,EAAEA;MAAU,CAAE,CAChE;MAAAW,QAAA,GAED/D,KAAA,CAACZ,IAAI;QAACwE,KAAK,EAAEC,MAAM,CAACY,WAAY;QAAAV,QAAA,GAC7BlB,gBAAgB,CAAC6B,MAAM,EAAC,2BAC3B;MAAA,CAAM,CAAC,EAEN7B,gBAAgB,CAAC8B,GAAG,CAAC,UAAC5B,OAAO,EAAK;QACjC,IAAM6B,eAAe,GAAGtC,kBAAkB,CAACS,OAAO,CAAC1C,UAAU,CAAC;QAE9D,OACEP,IAAA,CAACT,IAAI;UAAkBuE,KAAK,EAAEC,MAAM,CAACgB,WAAY;UAAAd,QAAA,EAC/C/D,KAAA,CAACX,IAAI,CAACyF,OAAO;YAAAf,QAAA,GACX/D,KAAA,CAAChB,IAAI;cAAC4E,KAAK,EAAEC,MAAM,CAACkB,aAAc;cAAAhB,QAAA,GAChC/D,KAAA,CAAChB,IAAI;gBAAC4E,KAAK,EAAEC,MAAM,CAACmB,WAAY;gBAAAjB,QAAA,GAC9BjE,IAAA,CAACV,IAAI;kBAACwE,KAAK,EAAEC,MAAM,CAACoB,WAAY;kBAAAlB,QAAA,EAAEhB,OAAO,CAAC5C;gBAAI,CAAO,CAAC,EACtDL,IAAA,CAACV,IAAI;kBAACwE,KAAK,EAAEC,MAAM,CAACqB,YAAa;kBAAAnB,QAAA,EAAEhB,OAAO,CAAClC;gBAAK,CAAO,CAAC,EAExDb,KAAA,CAAChB,IAAI;kBAAC4E,KAAK,EAAEC,MAAM,CAACsB,WAAY;kBAAApB,QAAA,GAC9B/D,KAAA,CAAChB,IAAI;oBAAC4E,KAAK,EAAEC,MAAM,CAACuB,QAAS;oBAAArB,QAAA,GAC3BjE,IAAA,CAACF,aAAa;sBACZO,IAAI,EAAEgC,eAAe,CAACY,OAAO,CAACtC,QAAQ,CAAE;sBACxC4E,IAAI,EAAE,EAAG;sBACTC,KAAK,EAAEvE,KAAK,CAACkB,MAAM,CAACsD;oBAAiB,CACtC,CAAC,EACFzF,IAAA,CAACV,IAAI;sBAACwE,KAAK,EAAEC,MAAM,CAAC2B,QAAS;sBAAAzB,QAAA,EAC1B3B,eAAe,CAACW,OAAO,CAACtC,QAAQ;oBAAC,CAC9B,CAAC;kBAAA,CACH,CAAC,EAEPT,KAAA,CAAChB,IAAI;oBAAC4E,KAAK,EAAEC,MAAM,CAACuB,QAAS;oBAAArB,QAAA,GAC3BjE,IAAA,CAACF,aAAa;sBACZO,IAAI,EAAC,WAAW;sBAChBkF,IAAI,EAAE,EAAG;sBACTC,KAAK,EAAEvE,KAAK,CAACkB,MAAM,CAACsD;oBAAiB,CACtC,CAAC,EACFvF,KAAA,CAACZ,IAAI;sBAACwE,KAAK,EAAEC,MAAM,CAAC2B,QAAS;sBAAAzB,QAAA,GAAC,wCACpB,EAAChB,OAAO,CAACrC,QAAQ;oBAAA,CACrB,CAAC;kBAAA,CACH,CAAC;gBAAA,CACH,CAAC;cAAA,CACH,CAAC,EAEPZ,IAAA,CAACR,IAAI;gBACHsE,KAAK,EAAE,CAACC,MAAM,CAAC4B,UAAU,EAAE;kBAAEC,eAAe,EAAE1D,cAAc,CAACe,OAAO,CAACvC,MAAM,CAAC,GAAG;gBAAK,CAAC,CAAE;gBACvFmF,SAAS,EAAE;kBAAEL,KAAK,EAAEtD,cAAc,CAACe,OAAO,CAACvC,MAAM,CAAC;kBAAEoF,QAAQ,EAAE;gBAAG,CAAE;gBAAA7B,QAAA,EAElE1B,aAAa,CAACU,OAAO,CAACvC,MAAM;cAAC,CAC1B,CAAC;YAAA,CACH,CAAC,EAEPR,KAAA,CAAChB,IAAI;cAAC4E,KAAK,EAAEC,MAAM,CAACgC,UAAW;cAAA9B,QAAA,GAC7B/D,KAAA,CAACZ,IAAI;gBAACwE,KAAK,EAAEC,MAAM,CAACxD,UAAW;gBAAA0D,QAAA,GAAC,yFACb,EAAChB,OAAO,CAAC1C,UAAU,CAACyF,kBAAkB,CAAC,OAAO,CAAC;cAAA,CAC5D,CAAC,EACPhG,IAAA,CAACV,IAAI;gBAACwE,KAAK,EAAE,CACXC,MAAM,CAACkC,aAAa,EACpB;kBAAET,KAAK,EAAEtD,cAAc,CAACe,OAAO,CAACvC,MAAM;gBAAE,CAAC,CACzC;gBAAAuD,QAAA,EACCa,eAAe,GAAG,CAAC,GAChB,GAAGA,eAAe,YAAY,GAC9BA,eAAe,KAAK,CAAC,GACnB,aAAa,GACb,aAAajC,IAAI,CAACqD,GAAG,CAACpB,eAAe,CAAC;cAAM,CAE9C,CAAC;YAAA,CACH,CAAC;UAAA,CACK;QAAC,GAxDN7B,OAAO,CAAC7C,EAyDb,CAAC;MAEX,CAAC,CAAC,EAED2C,gBAAgB,CAAC6B,MAAM,KAAK,CAAC,IAC5B1E,KAAA,CAAChB,IAAI;QAAC4E,KAAK,EAAEC,MAAM,CAACoC,UAAW;QAAAlC,QAAA,GAC7BjE,IAAA,CAACF,aAAa;UAACO,IAAI,EAAC,WAAW;UAACkF,IAAI,EAAE,EAAG;UAACC,KAAK,EAAEvE,KAAK,CAACkB,MAAM,CAACsD;QAAiB,CAAE,CAAC,EAClFzF,IAAA,CAACV,IAAI;UAACwE,KAAK,EAAEC,MAAM,CAACqC,SAAU;UAAAnC,QAAA,EAAC;QAAc,CAAM,CAAC,EACpDjE,IAAA,CAACV,IAAI;UAACwE,KAAK,EAAEC,MAAM,CAACsC,YAAa;UAAApC,QAAA,EAC9B5C,WAAW,GAAG,sCAAsC,GAAG;QAA4B,CAChF,CAAC;MAAA,CACH,CACP;IAAA,CACS,CAAC,EAEbrB,IAAA,CAACP,GAAG;MACFqE,KAAK,EAAEC,MAAM,CAACuC,GAAI;MAClBC,IAAI,EAAC,KAAK;MACVC,OAAO,EAAE,SAATA,OAAOA,CAAA,EAAQ,CAAwC;IAAE,CAC1D,CAAC;EAAA,CACU,CAAC;AAEnB,CAAC;AAED,IAAMzC,MAAM,GAAG5E,UAAU,CAACsH,MAAM,CAAC;EAC/BzC,SAAS,EAAE;IACT0C,IAAI,EAAE,CAAC;IACPd,eAAe,EAAE;EACnB,CAAC;EACD1B,MAAM,EAAE;IACNyC,OAAO,EAAE,EAAE;IACXf,eAAe,EAAE,OAAO;IACxBgB,SAAS,EAAE;EACb,CAAC;EACDvC,SAAS,EAAE;IACTwC,YAAY,EAAE;EAChB,CAAC;EACDrC,aAAa,EAAE;IACbqC,YAAY,EAAE;EAChB,CAAC;EACDpC,UAAU,EAAE;IACViC,IAAI,EAAE;EACR,CAAC;EACD/B,WAAW,EAAE;IACXmC,MAAM,EAAE,EAAE;IACVhB,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE;EACX,CAAC;EACDhC,WAAW,EAAE;IACXiC,gBAAgB,EAAE,EAAE;IACpBH,YAAY,EAAE,CAAC;IACfI,YAAY,EAAE;EAChB,CAAC;EACDhC,aAAa,EAAE;IACbiC,aAAa,EAAE,KAAK;IACpBC,cAAc,EAAE,eAAe;IAC/BC,UAAU,EAAE;EACd,CAAC;EACDlC,WAAW,EAAE;IACXwB,IAAI,EAAE;EACR,CAAC;EACDvB,WAAW,EAAE;IACXW,QAAQ,EAAE,EAAE;IACZuB,UAAU,EAAE;EACd,CAAC;EACDjC,YAAY,EAAE;IACZU,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,GAAG;IACZO,SAAS,EAAE;EACb,CAAC;EACDjC,WAAW,EAAE;IACX6B,aAAa,EAAE,KAAK;IACpBI,SAAS,EAAE,CAAC;IACZC,GAAG,EAAE;EACP,CAAC;EACDjC,QAAQ,EAAE;IACR4B,aAAa,EAAE,KAAK;IACpBE,UAAU,EAAE;EACd,CAAC;EACD1B,QAAQ,EAAE;IACR8B,UAAU,EAAE,CAAC;IACb1B,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE;EACX,CAAC;EACDpB,UAAU,EAAE;IACV6B,UAAU,EAAE;EACd,CAAC;EACDzB,UAAU,EAAE;IACVuB,SAAS,EAAE,EAAE;IACbG,UAAU,EAAE,EAAE;IACdC,cAAc,EAAE,CAAC;IACjBC,cAAc,EAAE;EAClB,CAAC;EACDpH,UAAU,EAAE;IACVuF,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE;EACX,CAAC;EACDd,aAAa,EAAE;IACbH,QAAQ,EAAE,EAAE;IACZuB,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE;EACb,CAAC;EACDnB,UAAU,EAAE;IACViB,UAAU,EAAE,QAAQ;IACpBD,cAAc,EAAE,QAAQ;IACxBR,OAAO,EAAE,EAAE;IACXW,SAAS,EAAE;EACb,CAAC;EACDlB,SAAS,EAAE;IACTN,QAAQ,EAAE,EAAE;IACZuB,UAAU,EAAE,MAAM;IAClBC,SAAS,EAAE;EACb,CAAC;EACDjB,YAAY,EAAE;IACZP,QAAQ,EAAE,EAAE;IACZiB,OAAO,EAAE,GAAG;IACZa,SAAS,EAAE,QAAQ;IACnBN,SAAS,EAAE;EACb,CAAC;EACDhB,GAAG,EAAE;IACHuB,QAAQ,EAAE,UAAU;IACpBf,MAAM,EAAE,EAAE;IACVgB,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE;EACV;AACF,CAAC,CAAC;AAEF,eAAe/G,sBAAsB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}