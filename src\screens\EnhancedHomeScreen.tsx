import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  RefreshControl,
  Dimensions,
} from 'react-native';
import {
  Text,
  Card,
  Title,
  Paragraph,
  Button,
  FAB,
  Chip,
  Surface,
  useTheme,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import { useNavigation } from '@react-navigation/native';

// بيانات وهمية للاختبار
const mockProducts = [
  {
    id: '1',
    name: 'حليب نادك',
    barcode: '123456789',
    expiryDate: new Date(Date.now() + 2 * 24 * 60 * 60 * 1000), // بعد يومين
    status: 'warning' as const,
    location: 'fridge' as const,
    quantity: 2,
    category: 'dairy',
    addedAt: new Date(),
  },
  {
    id: '2',
    name: 'خبز التميس',
    barcode: '987654321',
    expiryDate: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000), // بعد أسبوع
    status: 'fresh' as const,
    location: 'counter' as const,
    quantity: 1,
    category: 'bakery',
    addedAt: new Date(),
  },
  {
    id: '3',
    name: 'زبادي المراعي',
    barcode: '456789123',
    expiryDate: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000), // منتهي الصلاحية
    status: 'expired' as const,
    location: 'fridge' as const,
    quantity: 3,
    category: 'dairy',
    addedAt: new Date(),
  },
];

const { width } = Dimensions.get('window');

const EnhancedHomeScreen: React.FC = () => {
  const navigation = useNavigation();
  const theme = useTheme();
  const [refreshing, setRefreshing] = useState(false);

  // إحصائيات المنتجات
  const getProductStats = () => {
    const total = mockProducts.length;
    const fresh = mockProducts.filter(p => p.status === 'fresh').length;
    const warning = mockProducts.filter(p => p.status === 'warning').length;
    const expired = mockProducts.filter(p => p.status === 'expired').length;
    
    return { total, fresh, warning, expired };
  };

  const stats = getProductStats();

  const onRefresh = async () => {
    setRefreshing(true);
    // محاكاة تحديث البيانات
    await new Promise(resolve => setTimeout(resolve, 1000));
    setRefreshing(false);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'fresh': return '#4CAF50';
      case 'warning': return '#FF9800';
      case 'expired': return '#F44336';
      default: return theme.colors.primary;
    }
  };

  const getLocationIcon = (location: string) => {
    switch (location) {
      case 'fridge': return 'kitchen';
      case 'freezer': return 'ac-unit';
      case 'pantry': return 'store';
      case 'counter': return 'countertops';
      default: return 'inventory';
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
      >
        {/* ترحيب */}
        <Surface style={styles.welcomeCard}>
          <Title style={styles.welcomeTitle}>مرحباً بك في رفّك 👋</Title>
          <Paragraph style={styles.welcomeSubtitle}>
            تتبع منتجاتك وتواريخ انتهاء صلاحيتها بسهولة
          </Paragraph>
        </Surface>

        {/* إحصائيات سريعة */}
        <View style={styles.statsContainer}>
          <Text style={styles.sectionTitle}>الإحصائيات</Text>
          <View style={styles.statsRow}>
            <Card style={[styles.statCard, { backgroundColor: '#E8F5E8' }]}>
              <Card.Content style={styles.statContent}>
                <MaterialIcons name="inventory" size={24} color="#4CAF50" />
                <Text style={styles.statNumber}>{stats.total}</Text>
                <Text style={styles.statLabel}>إجمالي المنتجات</Text>
              </Card.Content>
            </Card>

            <Card style={[styles.statCard, { backgroundColor: '#FFF3E0' }]}>
              <Card.Content style={styles.statContent}>
                <MaterialIcons name="warning" size={24} color="#FF9800" />
                <Text style={styles.statNumber}>{stats.warning}</Text>
                <Text style={styles.statLabel}>تحتاج انتباه</Text>
              </Card.Content>
            </Card>

            <Card style={[styles.statCard, { backgroundColor: '#FFEBEE' }]}>
              <Card.Content style={styles.statContent}>
                <MaterialIcons name="error" size={24} color="#F44336" />
                <Text style={styles.statNumber}>{stats.expired}</Text>
                <Text style={styles.statLabel}>منتهية الصلاحية</Text>
              </Card.Content>
            </Card>
          </View>
        </View>

        {/* المنتجات الحديثة */}
        <View style={styles.recentContainer}>
          <Text style={styles.sectionTitle}>المنتجات الحديثة</Text>
          {mockProducts.map((product) => (
            <Card key={product.id} style={styles.productCard}>
              <Card.Content>
                <View style={styles.productHeader}>
                  <View style={styles.productInfo}>
                    <Text style={styles.productName}>{product.name}</Text>
                    <View style={styles.productMeta}>
                      <MaterialIcons 
                        name={getLocationIcon(product.location)} 
                        size={16} 
                        color={theme.colors.onSurfaceVariant} 
                      />
                      <Text style={styles.productLocation}>
                        {product.location === 'fridge' ? 'الثلاجة' : 
                         product.location === 'counter' ? 'المنضدة' : 'أخرى'}
                      </Text>
                    </View>
                  </View>
                  <Chip 
                    style={[styles.statusChip, { backgroundColor: getStatusColor(product.status) + '20' }]}
                    textStyle={{ color: getStatusColor(product.status) }}
                  >
                    {product.status === 'fresh' ? 'طازج' :
                     product.status === 'warning' ? 'تحذير' : 'منتهي'}
                  </Chip>
                </View>
                <Text style={styles.expiryDate}>
                  انتهاء الصلاحية: {product.expiryDate.toLocaleDateString('ar-SA')}
                </Text>
              </Card.Content>
            </Card>
          ))}
        </View>

        {/* أزرار سريعة */}
        <View style={styles.quickActions}>
          <Button 
            mode="contained" 
            icon="qr-code-scanner"
            style={styles.actionButton}
            onPress={() => {/* navigation.navigate('BarcodeScanner') */}}
          >
            مسح باركود
          </Button>
          <Button 
            mode="outlined" 
            icon="add"
            style={styles.actionButton}
            onPress={() => {/* navigation.navigate('AddProduct') */}}
          >
            إضافة منتج
          </Button>
        </View>
      </ScrollView>

      {/* زر الإضافة العائم */}
      <FAB
        style={styles.fab}
        icon="add"
        onPress={() => {/* navigation.navigate('AddProduct') */}}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  welcomeCard: {
    margin: 16,
    padding: 20,
    borderRadius: 12,
    elevation: 2,
  },
  welcomeTitle: {
    fontSize: 24,
    fontWeight: 'bold',
    textAlign: 'center',
    marginBottom: 8,
  },
  welcomeSubtitle: {
    textAlign: 'center',
    opacity: 0.7,
  },
  statsContainer: {
    margin: 16,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 12,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statCard: {
    flex: 1,
    marginHorizontal: 4,
    borderRadius: 12,
  },
  statContent: {
    alignItems: 'center',
    paddingVertical: 16,
  },
  statNumber: {
    fontSize: 24,
    fontWeight: 'bold',
    marginTop: 8,
  },
  statLabel: {
    fontSize: 12,
    textAlign: 'center',
    marginTop: 4,
  },
  recentContainer: {
    margin: 16,
  },
  productCard: {
    marginBottom: 8,
    borderRadius: 8,
  },
  productHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  productInfo: {
    flex: 1,
  },
  productName: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  productMeta: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: 4,
  },
  productLocation: {
    marginLeft: 4,
    fontSize: 12,
    opacity: 0.7,
  },
  statusChip: {
    marginLeft: 8,
  },
  expiryDate: {
    fontSize: 12,
    marginTop: 8,
    opacity: 0.7,
  },
  quickActions: {
    margin: 16,
    gap: 12,
  },
  actionButton: {
    marginBottom: 8,
  },
  fab: {
    position: 'absolute',
    margin: 16,
    right: 0,
    bottom: 0,
  },
});

export default EnhancedHomeScreen;
