{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nexport var IS_MANAGED_ENV = false;\nexport var IS_ENV_WITH_UPDATES_ENABLED = false;\nexport var IS_ENV_WITHOUT_UPDATES_ENABLED = false;\nexport var manifestBaseUrl = null;\nexport function downloadAsync(_x, _x2, _x3, _x4) {\n  return _downloadAsync.apply(this, arguments);\n}\nfunction _downloadAsync() {\n  _downloadAsync = _asyncToGenerator(function* (uri, hash, type, name) {\n    return uri;\n  });\n  return _downloadAsync.apply(this, arguments);\n}\nexport function getManifest() {\n  return {};\n}\nexport function getManifest2() {\n  return {};\n}", "map": {"version": 3, "names": ["IS_MANAGED_ENV", "IS_ENV_WITH_UPDATES_ENABLED", "IS_ENV_WITHOUT_UPDATES_ENABLED", "manifestBaseUrl", "downloadAsync", "_x", "_x2", "_x3", "_x4", "_downloadAsync", "apply", "arguments", "_asyncToGenerator", "uri", "hash", "type", "name", "getManifest", "getManifest2"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\expo-asset\\src\\PlatformUtils.web.ts"], "sourcesContent": ["export const IS_MANAGED_ENV = false;\nexport const IS_ENV_WITH_UPDATES_ENABLED = false;\nexport const IS_ENV_WITHOUT_UPDATES_ENABLED = false;\n\n// Compute manifest base URL if available\nexport const manifestBaseUrl = null;\n\nexport async function downloadAsync(uri, hash, type, name): Promise<string> {\n  return uri;\n}\n\nexport function getManifest() {\n  return {};\n}\n\nexport function getManifest2() {\n  return {};\n}\n"], "mappings": ";AAAA,OAAO,IAAMA,cAAc,GAAG,KAAK;AACnC,OAAO,IAAMC,2BAA2B,GAAG,KAAK;AAChD,OAAO,IAAMC,8BAA8B,GAAG,KAAK;AAGnD,OAAO,IAAMC,eAAe,GAAG,IAAI;AAEnC,gBAAsBC,aAAaA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAElC,SAAAF,eAAA;EAAAA,cAAA,GAAAG,iBAAA,CAFM,WAA6BC,GAAG,EAAEC,IAAI,EAAEC,IAAI,EAAEC,IAAI;IACvD,OAAOH,GAAG;EACZ,CAAC;EAAA,OAAAJ,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAED,OAAM,SAAUM,WAAWA,CAAA;EACzB,OAAO,EAAE;AACX;AAEA,OAAM,SAAUC,YAAYA,CAAA;EAC1B,OAAO,EAAE;AACX", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}