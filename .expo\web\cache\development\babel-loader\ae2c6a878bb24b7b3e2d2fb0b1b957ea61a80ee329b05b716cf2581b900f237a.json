{"ast": null, "code": "import _defineProperty from \"@babel/runtime/helpers/defineProperty\";\nfunction ownKeys(e, r) { var t = Object.keys(e); if (Object.getOwnPropertySymbols) { var o = Object.getOwnPropertySymbols(e); r && (o = o.filter(function (r) { return Object.getOwnPropertyDescriptor(e, r).enumerable; })), t.push.apply(t, o); } return t; }\nfunction _objectSpread(e) { for (var r = 1; r < arguments.length; r++) { var t = null != arguments[r] ? arguments[r] : {}; r % 2 ? ownKeys(Object(t), !0).forEach(function (r) { _defineProperty(e, r, t[r]); }) : Object.getOwnPropertyDescriptors ? Object.defineProperties(e, Object.getOwnPropertyDescriptors(t)) : ownKeys(Object(t)).forEach(function (r) { Object.defineProperty(e, r, Object.getOwnPropertyDescriptor(t, r)); }); } return e; }\nimport { CommonActions } from '@react-navigation/routers';\nimport * as React from 'react';\nimport NavigationContext from \"./NavigationContext\";\nimport { PrivateValueStore } from \"./types\";\nimport UnhandledActionContext from \"./UnhandledActionContext\";\nPrivateValueStore;\nexport default function useNavigationHelpers(_ref) {\n  var navigatorId = _ref.id,\n    onAction = _ref.onAction,\n    getState = _ref.getState,\n    emitter = _ref.emitter,\n    router = _ref.router;\n  var onUnhandledAction = React.useContext(UnhandledActionContext);\n  var parentNavigationHelpers = React.useContext(NavigationContext);\n  return React.useMemo(function () {\n    var dispatch = function dispatch(op) {\n      var action = typeof op === 'function' ? op(getState()) : op;\n      var handled = onAction(action);\n      if (!handled) {\n        onUnhandledAction === null || onUnhandledAction === void 0 ? void 0 : onUnhandledAction(action);\n      }\n    };\n    var actions = _objectSpread(_objectSpread({}, router.actionCreators), CommonActions);\n    var helpers = Object.keys(actions).reduce(function (acc, name) {\n      acc[name] = function () {\n        return dispatch(actions[name].apply(actions, arguments));\n      };\n      return acc;\n    }, {});\n    var navigationHelpers = _objectSpread(_objectSpread(_objectSpread({}, parentNavigationHelpers), helpers), {}, {\n      dispatch: dispatch,\n      emit: emitter.emit,\n      isFocused: parentNavigationHelpers ? parentNavigationHelpers.isFocused : function () {\n        return true;\n      },\n      canGoBack: function canGoBack() {\n        var state = getState();\n        return router.getStateForAction(state, CommonActions.goBack(), {\n          routeNames: state.routeNames,\n          routeParamList: {},\n          routeGetIdList: {}\n        }) !== null || (parentNavigationHelpers === null || parentNavigationHelpers === void 0 ? void 0 : parentNavigationHelpers.canGoBack()) || false;\n      },\n      getId: function getId() {\n        return navigatorId;\n      },\n      getParent: function getParent(id) {\n        if (id !== undefined) {\n          var current = navigationHelpers;\n          while (current && id !== current.getId()) {\n            current = current.getParent();\n          }\n          return current;\n        }\n        return parentNavigationHelpers;\n      },\n      getState: getState\n    });\n    return navigationHelpers;\n  }, [navigatorId, emitter.emit, getState, onAction, onUnhandledAction, parentNavigationHelpers, router]);\n}", "map": {"version": 3, "names": ["CommonActions", "React", "NavigationContext", "PrivateValueStore", "UnhandledActionContext", "useNavigationHelpers", "_ref", "navigatorId", "id", "onAction", "getState", "emitter", "router", "onUnhandledAction", "useContext", "parentNavigationHelpers", "useMemo", "dispatch", "op", "action", "handled", "actions", "_objectSpread", "actionCreators", "helpers", "Object", "keys", "reduce", "acc", "name", "apply", "arguments", "navigationHelpers", "emit", "isFocused", "canGoBack", "state", "getStateForAction", "goBack", "routeNames", "routeParamList", "routeGetIdList", "getId", "getParent", "undefined", "current"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\@react-navigation\\core\\src\\useNavigationHelpers.tsx"], "sourcesContent": ["import {\n  CommonActions,\n  NavigationAction,\n  NavigationState,\n  ParamListBase,\n  Router,\n} from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationContext from './NavigationContext';\nimport { NavigationHelpers, PrivateValueStore } from './types';\nimport UnhandledActionContext from './UnhandledActionContext';\nimport type { NavigationEventEmitter } from './useEventEmitter';\n\n// This is to make TypeScript compiler happy\n// eslint-disable-next-line babel/no-unused-expressions\nPrivateValueStore;\n\ntype Options<State extends NavigationState, Action extends NavigationAction> = {\n  id: string | undefined;\n  onAction: (action: NavigationAction) => boolean;\n  getState: () => State;\n  emitter: NavigationEventEmitter<any>;\n  router: Router<State, Action>;\n};\n\n/**\n * Navigation object with helper methods to be used by a navigator.\n * This object includes methods for common actions as well as methods the parent screen's navigation object.\n */\nexport default function useNavigationHelpers<\n  State extends NavigationState,\n  ActionHelpers extends Record<string, () => void>,\n  Action extends NavigationAction,\n  EventMap extends Record<string, any>\n>({\n  id: navigatorId,\n  onAction,\n  getState,\n  emitter,\n  router,\n}: Options<State, Action>) {\n  const onUnhandledAction = React.useContext(UnhandledActionContext);\n  const parentNavigationHelpers = React.useContext(NavigationContext);\n\n  return React.useMemo(() => {\n    const dispatch = (op: Action | ((state: State) => Action)) => {\n      const action = typeof op === 'function' ? op(getState()) : op;\n\n      const handled = onAction(action);\n\n      if (!handled) {\n        onUnhandledAction?.(action);\n      }\n    };\n\n    const actions = {\n      ...router.actionCreators,\n      ...CommonActions,\n    };\n\n    const helpers = Object.keys(actions).reduce((acc, name) => {\n      // @ts-expect-error: name is a valid key, but TypeScript is dumb\n      acc[name] = (...args: any) => dispatch(actions[name](...args));\n      return acc;\n    }, {} as ActionHelpers);\n\n    const navigationHelpers = {\n      ...parentNavigationHelpers,\n      ...helpers,\n      dispatch,\n      emit: emitter.emit,\n      isFocused: parentNavigationHelpers\n        ? parentNavigationHelpers.isFocused\n        : () => true,\n      canGoBack: () => {\n        const state = getState();\n\n        return (\n          router.getStateForAction(state, CommonActions.goBack() as Action, {\n            routeNames: state.routeNames,\n            routeParamList: {},\n            routeGetIdList: {},\n          }) !== null ||\n          parentNavigationHelpers?.canGoBack() ||\n          false\n        );\n      },\n      getId: () => navigatorId,\n      getParent: (id?: string) => {\n        if (id !== undefined) {\n          let current = navigationHelpers;\n\n          while (current && id !== current.getId()) {\n            current = current.getParent();\n          }\n\n          return current;\n        }\n\n        return parentNavigationHelpers;\n      },\n      getState,\n    } as NavigationHelpers<ParamListBase, EventMap> & ActionHelpers;\n\n    return navigationHelpers;\n  }, [\n    navigatorId,\n    emitter.emit,\n    getState,\n    onAction,\n    onUnhandledAction,\n    parentNavigationHelpers,\n    router,\n  ]);\n}\n"], "mappings": ";;;AAAA,SACEA,aAAa,QAKR,2BAA2B;AAClC,OAAO,KAAKC,KAAK,MAAM,OAAO;AAE9B,OAAOC,iBAAiB;AACxB,SAA4BC,iBAAiB;AAC7C,OAAOC,sBAAsB;AAK7BD,iBAAiB;AAcjB,eAAe,SAASE,oBAAoBA,CAAAC,IAAA,EAWjB;EAAA,IALrBC,WAAW,GAKQD,IAAA,CALvBE,EAAE;IACFC,QAAQ,GAIeH,IAAA,CAJvBG,QAAQ;IACRC,QAAQ,GAGeJ,IAAA,CAHvBI,QAAQ;IACRC,OAAO,GAEgBL,IAAA,CAFvBK,OAAO;IACPC,MAAA,GACuBN,IAAA,CADvBM,MAAA;EAEA,IAAMC,iBAAiB,GAAGZ,KAAK,CAACa,UAAU,CAACV,sBAAsB,CAAC;EAClE,IAAMW,uBAAuB,GAAGd,KAAK,CAACa,UAAU,CAACZ,iBAAiB,CAAC;EAEnE,OAAOD,KAAK,CAACe,OAAO,CAAC,YAAM;IACzB,IAAMC,QAAQ,GAAI,SAAZA,QAAQA,CAAIC,EAAuC,EAAK;MAC5D,IAAMC,MAAM,GAAG,OAAOD,EAAE,KAAK,UAAU,GAAGA,EAAE,CAACR,QAAQ,EAAE,CAAC,GAAGQ,EAAE;MAE7D,IAAME,OAAO,GAAGX,QAAQ,CAACU,MAAM,CAAC;MAEhC,IAAI,CAACC,OAAO,EAAE;QACZP,iBAAiB,aAAjBA,iBAAiB,uBAAjBA,iBAAiB,CAAGM,MAAM,CAAC;MAC7B;IACF,CAAC;IAED,IAAME,OAAO,GAAAC,aAAA,CAAAA,aAAA,KACRV,MAAM,CAACW,cAAc,GACrBvB,aAAA,CACJ;IAED,IAAMwB,OAAO,GAAGC,MAAM,CAACC,IAAI,CAACL,OAAO,CAAC,CAACM,MAAM,CAAC,UAACC,GAAG,EAAEC,IAAI,EAAK;MAEzDD,GAAG,CAACC,IAAI,CAAC,GAAG;QAAA,OAAkBZ,QAAQ,CAACI,OAAO,CAACQ,IAAI,CAAC,CAAAC,KAAA,CAAbT,OAAO,EAAOU,SAAO,CAAC,CAAC;MAAA;MAC9D,OAAOH,GAAG;IACZ,CAAC,EAAE,CAAC,CAAC,CAAkB;IAEvB,IAAMI,iBAAiB,GAAAV,aAAA,CAAAA,aAAA,CAAAA,aAAA,KAClBP,uBAAuB,GACvBS,OAAO;MACVP,QAAQ,EAARA,QAAQ;MACRgB,IAAI,EAAEtB,OAAO,CAACsB,IAAI;MAClBC,SAAS,EAAEnB,uBAAuB,GAC9BA,uBAAuB,CAACmB,SAAS,GACjC;QAAA,OAAM,IAAI;MAAA;MACdC,SAAS,EAAE,SAAXA,SAASA,CAAA,EAAQ;QACf,IAAMC,KAAK,GAAG1B,QAAQ,EAAE;QAExB,OACEE,MAAM,CAACyB,iBAAiB,CAACD,KAAK,EAAEpC,aAAa,CAACsC,MAAM,EAAE,EAAY;UAChEC,UAAU,EAAEH,KAAK,CAACG,UAAU;UAC5BC,cAAc,EAAE,CAAC,CAAC;UAClBC,cAAc,EAAE,CAAC;QACnB,CAAC,CAAC,KAAK,IAAI,KACX1B,uBAAuB,aAAvBA,uBAAuB,uBAAvBA,uBAAuB,CAAEoB,SAAS,EAAE,KACpC,KAAK;MAET,CAAC;MACDO,KAAK,EAAE,SAAPA,KAAKA,CAAA;QAAA,OAAQnC,WAAW;MAAA;MACxBoC,SAAS,EAAG,SAAZA,SAASA,CAAGnC,EAAW,EAAK;QAC1B,IAAIA,EAAE,KAAKoC,SAAS,EAAE;UACpB,IAAIC,OAAO,GAAGb,iBAAiB;UAE/B,OAAOa,OAAO,IAAIrC,EAAE,KAAKqC,OAAO,CAACH,KAAK,EAAE,EAAE;YACxCG,OAAO,GAAGA,OAAO,CAACF,SAAS,EAAE;UAC/B;UAEA,OAAOE,OAAO;QAChB;QAEA,OAAO9B,uBAAuB;MAChC,CAAC;MACDL,QAAA,EAAAA;IAAA,EAC6D;IAE/D,OAAOsB,iBAAiB;EAC1B,CAAC,EAAE,CACDzB,WAAW,EACXI,OAAO,CAACsB,IAAI,EACZvB,QAAQ,EACRD,QAAQ,EACRI,iBAAiB,EACjBE,uBAAuB,EACvBH,MAAM,CACP,CAAC;AACJ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}