{"name": "shelf-track", "version": "1.0.0", "description": "تطبيق رفّك لمتابعة صلاحية المنتجات", "main": "node_modules/expo/AppEntry.js", "scripts": {"start": "expo start", "android": "expo start --android", "ios": "expo start --ios", "web": "expo start --web", "eject": "expo eject", "test": "jest"}, "dependencies": {"@expo/vector-icons": "^13.0.0", "@react-native-async-storage/async-storage": "1.18.2", "@react-native-community/datetimepicker": "7.2.0", "@react-navigation/bottom-tabs": "^6.5.8", "@react-navigation/native": "^6.1.7", "@react-navigation/stack": "^6.3.17", "expo": "~49.0.10", "expo-barcode-scanner": "~12.5.3", "expo-camera": "~13.4.4", "expo-constants": "~14.4.2", "expo-device": "~5.4.0", "expo-font": "~11.4.0", "expo-linking": "~5.0.2", "expo-notifications": "~0.20.1", "expo-permissions": "~14.2.1", "expo-splash-screen": "~0.20.5", "expo-status-bar": "~1.6.0", "firebase": "^10.3.1", "react": "18.2.0", "react-native": "0.72.6", "react-native-gesture-handler": "~2.12.0", "react-native-modal-datetime-picker": "^17.1.0", "react-native-paper": "^5.10.4", "react-native-reanimated": "~3.3.0", "react-native-safe-area-context": "4.6.3", "react-native-screens": "~3.22.0", "react-native-share": "^9.4.1", "react-native-vector-icons": "^10.0.0"}, "devDependencies": {"@babel/core": "^7.20.0", "@types/react": "~18.2.14", "@types/react-native": "~0.72.2", "jest": "^29.2.1", "typescript": "^5.1.3"}, "private": true, "keywords": ["react-native", "expo", "barcode-scanner", "product-tracking", "arabic"], "author": "ShelfTrack Team", "license": "MIT"}