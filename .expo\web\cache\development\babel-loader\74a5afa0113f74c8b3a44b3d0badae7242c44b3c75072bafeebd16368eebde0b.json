{"ast": null, "code": "import _asyncToGenerator from \"@babel/runtime/helpers/asyncToGenerator\";\nimport _slicedToArray from \"@babel/runtime/helpers/slicedToArray\";\nimport React, { useEffect, useState } from 'react';\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport { Provider as PaperProvider } from 'react-native-paper';\nimport I18nManager from \"react-native-web/dist/exports/I18nManager\";\nimport Platform from \"react-native-web/dist/exports/Platform\";\nimport * as SplashScreen from 'expo-splash-screen';\nimport * as Font from 'expo-font';\nimport { useAuth } from \"./src/services/AuthContext\";\nimport { initializeFirebase } from \"./src/services/firebase\";\nimport AuthScreen from \"./src/screens/AuthScreen\";\nimport MainNavigator from \"./src/navigation/MainNavigator\";\nimport LoadingScreen from \"./src/screens/LoadingScreen\";\nimport { lightTheme, darkTheme } from \"./src/constants/theme\";\nimport useColorScheme from \"react-native-web/dist/exports/useColorScheme\";\nimport { jsx as _jsx, jsxs as _jsxs } from \"react/jsx-runtime\";\nvar Stack = createStackNavigator();\nSplashScreen.preventAutoHideAsync();\nif (Platform.OS !== 'web') {\n  I18nManager.allowRTL(true);\n  I18nManager.forceRTL(true);\n}\nfunction AppContent() {\n  var _useAuth = useAuth(),\n    user = _useAuth.user,\n    isLoading = _useAuth.isLoading;\n  var colorScheme = useColorScheme();\n  var _useState = useState(false),\n    _useState2 = _slicedToArray(_useState, 2),\n    appIsReady = _useState2[0],\n    setAppIsReady = _useState2[1];\n  useEffect(function () {\n    function prepare() {\n      return _prepare.apply(this, arguments);\n    }\n    function _prepare() {\n      _prepare = _asyncToGenerator(function* () {\n        try {\n          yield Font.loadAsync({});\n          yield initializeFirebase();\n          yield new Promise(function (resolve) {\n            return setTimeout(resolve, 2000);\n          });\n        } catch (e) {\n          console.warn(e);\n        } finally {\n          setAppIsReady(true);\n        }\n      });\n      return _prepare.apply(this, arguments);\n    }\n    prepare();\n  }, []);\n  useEffect(function () {\n    if (appIsReady) {\n      SplashScreen.hideAsync();\n    }\n  }, [appIsReady]);\n  if (!appIsReady || isLoading) {\n    return _jsx(LoadingScreen, {});\n  }\n  var theme = colorScheme === 'dark' ? darkTheme : lightTheme;\n  return _jsx(PaperProvider, {\n    theme: theme,\n    children: _jsxs(NavigationContainer, {\n      theme: theme,\n      children: [_jsx(StatusBar, {\n        style: colorScheme === 'dark' ? 'light' : 'dark'\n      }), _jsx(Stack.Navigator, {\n        screenOptions: {\n          headerShown: false,\n          gestureEnabled: true,\n          gestureDirection: 'horizontal-inverted'\n        },\n        children: user ? _jsx(Stack.Screen, {\n          name: \"Main\",\n          component: MainNavigator\n        }) : _jsx(Stack.Screen, {\n          name: \"Auth\",\n          component: AuthScreen\n        })\n      })]\n    })\n  });\n}\nfunction SimpleApp() {\n  var colorScheme = useColorScheme();\n  var theme = colorScheme === 'dark' ? darkTheme : lightTheme;\n  return _jsx(PaperProvider, {\n    theme: theme,\n    children: _jsxs(NavigationContainer, {\n      theme: theme,\n      children: [_jsx(StatusBar, {\n        style: colorScheme === 'dark' ? 'light' : 'dark'\n      }), _jsx(MainNavigator, {})]\n    })\n  });\n}\nexport default function App() {\n  return _jsx(SimpleApp, {});\n}", "map": {"version": 3, "names": ["React", "useEffect", "useState", "StatusBar", "NavigationContainer", "createStackNavigator", "Provider", "PaperProvider", "I18nManager", "Platform", "SplashScreen", "Font", "useAuth", "initializeFirebase", "AuthScreen", "MainNavigator", "LoadingScreen", "lightTheme", "darkTheme", "useColorScheme", "jsx", "_jsx", "jsxs", "_jsxs", "<PERSON><PERSON>", "preventAutoHideAsync", "OS", "allowRTL", "forceRTL", "A<PERSON><PERSON><PERSON>nt", "_useAuth", "user", "isLoading", "colorScheme", "_useState", "_useState2", "_slicedToArray", "appIsReady", "setAppIsReady", "prepare", "_prepare", "apply", "arguments", "_asyncToGenerator", "loadAsync", "Promise", "resolve", "setTimeout", "e", "console", "warn", "<PERSON><PERSON><PERSON>", "theme", "children", "style", "Navigator", "screenOptions", "headerShown", "gestureEnabled", "gestureDirection", "Screen", "name", "component", "SimpleApp", "App"], "sources": ["C:/Users/<USER>/Music/mobile/App.tsx"], "sourcesContent": ["import React, { useEffect, useState } from 'react';\nimport { StatusBar } from 'expo-status-bar';\nimport { NavigationContainer } from '@react-navigation/native';\nimport { createStackNavigator } from '@react-navigation/stack';\nimport { Provider as PaperProvider } from 'react-native-paper';\nimport { I18nManager, Platform } from 'react-native';\nimport * as SplashScreen from 'expo-splash-screen';\nimport * as Font from 'expo-font';\n\n// الخدمات\nimport { AuthProvider, useAuth } from '@/services/AuthContext';\nimport { ProductProvider } from '@/services/ProductContext';\nimport { NotificationProvider } from '@/services/NotificationContext';\nimport { initializeFirebase } from '@/services/firebase';\n\n// الشاشات\nimport AuthScreen from '@/screens/AuthScreen';\nimport MainNavigator from '@/navigation/MainNavigator';\nimport LoadingScreen from '@/screens/LoadingScreen';\n\n// الثيم والألوان\nimport { lightTheme, darkTheme } from '@/constants/theme';\nimport { useColorScheme } from 'react-native';\n\n// أنواع البيانات\nimport { RootStackParamList } from '@/types';\n\nconst Stack = createStackNavigator<RootStackParamList>();\n\n// منع إخفاء شاشة التحميل تلقائياً\nSplashScreen.preventAutoHideAsync();\n\n// تفعيل RTL للغة العربية\nif (Platform.OS !== 'web') {\n  I18nManager.allowRTL(true);\n  I18nManager.forceRTL(true);\n}\n\nfunction AppContent() {\n  const { user, isLoading } = useAuth();\n  const colorScheme = useColorScheme();\n  const [appIsReady, setAppIsReady] = useState(false);\n\n  useEffect(() => {\n    async function prepare() {\n      try {\n        // تحميل الخطوط\n        await Font.loadAsync({\n          // يمكن إضافة خطوط عربية مخصصة هنا\n        });\n\n        // تهيئة Firebase\n        await initializeFirebase();\n\n        // محاكاة وقت التحميل\n        await new Promise(resolve => setTimeout(resolve, 2000));\n      } catch (e) {\n        console.warn(e);\n      } finally {\n        setAppIsReady(true);\n      }\n    }\n\n    prepare();\n  }, []);\n\n  useEffect(() => {\n    if (appIsReady) {\n      SplashScreen.hideAsync();\n    }\n  }, [appIsReady]);\n\n  if (!appIsReady || isLoading) {\n    return <LoadingScreen />;\n  }\n\n  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;\n\n  return (\n    <PaperProvider theme={theme}>\n      <NavigationContainer theme={theme}>\n        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />\n        <Stack.Navigator\n          screenOptions={{\n            headerShown: false,\n            gestureEnabled: true,\n            gestureDirection: 'horizontal-inverted', // RTL gesture\n          }}\n        >\n          {user ? (\n            <Stack.Screen name=\"Main\" component={MainNavigator} />\n          ) : (\n            <Stack.Screen name=\"Auth\" component={AuthScreen} />\n          )}\n        </Stack.Navigator>\n      </NavigationContainer>\n    </PaperProvider>\n  );\n}\n\n// نسخة مبسطة للاختبار بدون Firebase\nfunction SimpleApp() {\n  const colorScheme = useColorScheme();\n  const theme = colorScheme === 'dark' ? darkTheme : lightTheme;\n\n  return (\n    <PaperProvider theme={theme}>\n      <NavigationContainer theme={theme}>\n        <StatusBar style={colorScheme === 'dark' ? 'light' : 'dark'} />\n        <MainNavigator />\n      </NavigationContainer>\n    </PaperProvider>\n  );\n}\n\nexport default function App() {\n  // استخدام النسخة المبسطة للاختبار\n  return <SimpleApp />;\n\n  // النسخة الكاملة مع Firebase (معطلة مؤقتاً)\n  /*\n  return (\n    <AuthProvider>\n      <ProductProvider>\n        <NotificationProvider>\n          <AppContent />\n        </NotificationProvider>\n      </ProductProvider>\n    </AuthProvider>\n  );\n  */\n}\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,SAAS,QAAQ,iBAAiB;AAC3C,SAASC,mBAAmB,QAAQ,0BAA0B;AAC9D,SAASC,oBAAoB,QAAQ,yBAAyB;AAC9D,SAASC,QAAQ,IAAIC,aAAa,QAAQ,oBAAoB;AAAC,OAAAC,WAAA;AAAA,OAAAC,QAAA;AAE/D,OAAO,KAAKC,YAAY,MAAM,oBAAoB;AAClD,OAAO,KAAKC,IAAI,MAAM,WAAW;AAGjC,SAAuBC,OAAO;AAG9B,SAASC,kBAAkB;AAG3B,OAAOC,UAAU;AACjB,OAAOC,aAAa;AACpB,OAAOC,aAAa;AAGpB,SAASC,UAAU,EAAEC,SAAS;AAA4B,OAAAC,cAAA;AAAA,SAAAC,GAAA,IAAAC,IAAA,EAAAC,IAAA,IAAAC,KAAA;AAM1D,IAAMC,KAAK,GAAGnB,oBAAoB,CAAqB,CAAC;AAGxDK,YAAY,CAACe,oBAAoB,CAAC,CAAC;AAGnC,IAAIhB,QAAQ,CAACiB,EAAE,KAAK,KAAK,EAAE;EACzBlB,WAAW,CAACmB,QAAQ,CAAC,IAAI,CAAC;EAC1BnB,WAAW,CAACoB,QAAQ,CAAC,IAAI,CAAC;AAC5B;AAEA,SAASC,UAAUA,CAAA,EAAG;EACpB,IAAAC,QAAA,GAA4BlB,OAAO,CAAC,CAAC;IAA7BmB,IAAI,GAAAD,QAAA,CAAJC,IAAI;IAAEC,SAAS,GAAAF,QAAA,CAATE,SAAS;EACvB,IAAMC,WAAW,GAAGd,cAAc,CAAC,CAAC;EACpC,IAAAe,SAAA,GAAoChC,QAAQ,CAAC,KAAK,CAAC;IAAAiC,UAAA,GAAAC,cAAA,CAAAF,SAAA;IAA5CG,UAAU,GAAAF,UAAA;IAAEG,aAAa,GAAAH,UAAA;EAEhClC,SAAS,CAAC,YAAM;IAAA,SACCsC,OAAOA,CAAA;MAAA,OAAAC,QAAA,CAAAC,KAAA,OAAAC,SAAA;IAAA;IAAA,SAAAF,SAAA;MAAAA,QAAA,GAAAG,iBAAA,CAAtB,aAAyB;QACvB,IAAI;UAEF,MAAMhC,IAAI,CAACiC,SAAS,CAAC,CAErB,CAAC,CAAC;UAGF,MAAM/B,kBAAkB,CAAC,CAAC;UAG1B,MAAM,IAAIgC,OAAO,CAAC,UAAAC,OAAO;YAAA,OAAIC,UAAU,CAACD,OAAO,EAAE,IAAI,CAAC;UAAA,EAAC;QACzD,CAAC,CAAC,OAAOE,CAAC,EAAE;UACVC,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;QACjB,CAAC,SAAS;UACRV,aAAa,CAAC,IAAI,CAAC;QACrB;MACF,CAAC;MAAA,OAAAE,QAAA,CAAAC,KAAA,OAAAC,SAAA;IAAA;IAEDH,OAAO,CAAC,CAAC;EACX,CAAC,EAAE,EAAE,CAAC;EAENtC,SAAS,CAAC,YAAM;IACd,IAAIoC,UAAU,EAAE;MACd3B,YAAY,CAACyC,SAAS,CAAC,CAAC;IAC1B;EACF,CAAC,EAAE,CAACd,UAAU,CAAC,CAAC;EAEhB,IAAI,CAACA,UAAU,IAAIL,SAAS,EAAE;IAC5B,OAAOX,IAAA,CAACL,aAAa,IAAE,CAAC;EAC1B;EAEA,IAAMoC,KAAK,GAAGnB,WAAW,KAAK,MAAM,GAAGf,SAAS,GAAGD,UAAU;EAE7D,OACEI,IAAA,CAACd,aAAa;IAAC6C,KAAK,EAAEA,KAAM;IAAAC,QAAA,EAC1B9B,KAAA,CAACnB,mBAAmB;MAACgD,KAAK,EAAEA,KAAM;MAAAC,QAAA,GAChChC,IAAA,CAAClB,SAAS;QAACmD,KAAK,EAAErB,WAAW,KAAK,MAAM,GAAG,OAAO,GAAG;MAAO,CAAE,CAAC,EAC/DZ,IAAA,CAACG,KAAK,CAAC+B,SAAS;QACdC,aAAa,EAAE;UACbC,WAAW,EAAE,KAAK;UAClBC,cAAc,EAAE,IAAI;UACpBC,gBAAgB,EAAE;QACpB,CAAE;QAAAN,QAAA,EAEDtB,IAAI,GACHV,IAAA,CAACG,KAAK,CAACoC,MAAM;UAACC,IAAI,EAAC,MAAM;UAACC,SAAS,EAAE/C;QAAc,CAAE,CAAC,GAEtDM,IAAA,CAACG,KAAK,CAACoC,MAAM;UAACC,IAAI,EAAC,MAAM;UAACC,SAAS,EAAEhD;QAAW,CAAE;MACnD,CACc,CAAC;IAAA,CACC;EAAC,CACT,CAAC;AAEpB;AAGA,SAASiD,SAASA,CAAA,EAAG;EACnB,IAAM9B,WAAW,GAAGd,cAAc,CAAC,CAAC;EACpC,IAAMiC,KAAK,GAAGnB,WAAW,KAAK,MAAM,GAAGf,SAAS,GAAGD,UAAU;EAE7D,OACEI,IAAA,CAACd,aAAa;IAAC6C,KAAK,EAAEA,KAAM;IAAAC,QAAA,EAC1B9B,KAAA,CAACnB,mBAAmB;MAACgD,KAAK,EAAEA,KAAM;MAAAC,QAAA,GAChChC,IAAA,CAAClB,SAAS;QAACmD,KAAK,EAAErB,WAAW,KAAK,MAAM,GAAG,OAAO,GAAG;MAAO,CAAE,CAAC,EAC/DZ,IAAA,CAACN,aAAa,IAAE,CAAC;IAAA,CACE;EAAC,CACT,CAAC;AAEpB;AAEA,eAAe,SAASiD,GAAGA,CAAA,EAAG;EAE5B,OAAO3C,IAAA,CAAC0C,SAAS,IAAE,CAAC;AActB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}