{"ast": null, "code": "'use strict';\n\nimport _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _possibleConstructorReturn from \"@babel/runtime/helpers/possibleConstructorReturn\";\nimport _getPrototypeOf from \"@babel/runtime/helpers/getPrototypeOf\";\nimport _get from \"@babel/runtime/helpers/get\";\nimport _inherits from \"@babel/runtime/helpers/inherits\";\nfunction _callSuper(t, o, e) { return o = _getPrototypeOf(o), _possibleConstructorReturn(t, _isNativeReflectConstruct() ? Reflect.construct(o, e || [], _getPrototypeOf(t).constructor) : o.apply(t, e)); }\nfunction _isNativeReflectConstruct() { try { var t = !Boolean.prototype.valueOf.call(Reflect.construct(Boolean, [], function () {})); } catch (t) {} return (_isNativeReflectConstruct = function _isNativeReflectConstruct() { return !!t; })(); }\nfunction _superPropGet(t, o, e, r) { var p = _get(_getPrototypeOf(1 & r ? t.prototype : t), o, e); return 2 & r && \"function\" == typeof p ? function (t) { return p.apply(e, t); } : p; }\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport AnimatedNode from \"./AnimatedNode\";\nimport NativeAnimatedHelper from \"../NativeAnimatedHelper\";\nvar AnimatedWithChildren = function (_AnimatedNode) {\n  function AnimatedWithChildren() {\n    var _this;\n    _classCallCheck(this, AnimatedWithChildren);\n    _this = _callSuper(this, AnimatedWithChildren);\n    _this._children = [];\n    return _this;\n  }\n  _inherits(AnimatedWithChildren, _AnimatedNode);\n  return _createClass(AnimatedWithChildren, [{\n    key: \"__makeNative\",\n    value: function __makeNative(platformConfig) {\n      if (!this.__isNative) {\n        this.__isNative = true;\n        for (var _iterator = _createForOfIteratorHelperLoose(this._children), _step; !(_step = _iterator()).done;) {\n          var child = _step.value;\n          child.__makeNative(platformConfig);\n          NativeAnimatedHelper.API.connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n        }\n      }\n      _superPropGet(AnimatedWithChildren, \"__makeNative\", this, 3)([platformConfig]);\n    }\n  }, {\n    key: \"__addChild\",\n    value: function __addChild(child) {\n      if (this._children.length === 0) {\n        this.__attach();\n      }\n      this._children.push(child);\n      if (this.__isNative) {\n        child.__makeNative(this.__getPlatformConfig());\n        NativeAnimatedHelper.API.connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n      }\n    }\n  }, {\n    key: \"__removeChild\",\n    value: function __removeChild(child) {\n      var index = this._children.indexOf(child);\n      if (index === -1) {\n        console.warn(\"Trying to remove a child that doesn't exist\");\n        return;\n      }\n      if (this.__isNative && child.__isNative) {\n        NativeAnimatedHelper.API.disconnectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n      }\n      this._children.splice(index, 1);\n      if (this._children.length === 0) {\n        this.__detach();\n      }\n    }\n  }, {\n    key: \"__getChildren\",\n    value: function __getChildren() {\n      return this._children;\n    }\n  }, {\n    key: \"__callListeners\",\n    value: function __callListeners(value) {\n      _superPropGet(AnimatedWithChildren, \"__callListeners\", this, 3)([value]);\n      if (!this.__isNative) {\n        for (var _iterator2 = _createForOfIteratorHelperLoose(this._children), _step2; !(_step2 = _iterator2()).done;) {\n          var child = _step2.value;\n          if (child.__getValue) {\n            child.__callListeners(child.__getValue());\n          }\n        }\n      }\n    }\n  }]);\n}(AnimatedNode);\nexport default AnimatedWithChildren;", "map": {"version": 3, "names": ["_classCallCheck", "_createClass", "_possibleConstructorReturn", "_getPrototypeOf", "_get", "_inherits", "_callSuper", "t", "o", "e", "_isNativeReflectConstruct", "Reflect", "construct", "constructor", "apply", "Boolean", "prototype", "valueOf", "call", "_superPropGet", "r", "p", "_createForOfIteratorHelperLoose", "AnimatedNode", "NativeAnimatedHelper", "AnimatedWithChildren", "_AnimatedNode", "_this", "_children", "key", "value", "__makeNative", "platformConfig", "__isNative", "_iterator", "_step", "done", "child", "API", "connectAnimatedNodes", "__getNativeTag", "__add<PERSON><PERSON>d", "length", "__attach", "push", "__getPlatformConfig", "__remove<PERSON><PERSON>d", "index", "indexOf", "console", "warn", "disconnectAnimatedNodes", "splice", "__detach", "__get<PERSON><PERSON><PERSON><PERSON>", "__callListeners", "_iterator2", "_step2", "__getValue"], "sources": ["C:/Users/<USER>/Music/mobile/node_modules/react-native-web/dist/vendor/react-native/Animated/nodes/AnimatedWithChildren.js"], "sourcesContent": ["/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\n'use strict';\n\nimport _createForOfIteratorHelperLoose from \"@babel/runtime/helpers/createForOfIteratorHelperLoose\";\nimport AnimatedNode from './AnimatedNode';\nimport NativeAnimatedHelper from '../NativeAnimatedHelper';\nclass AnimatedWithChildren extends AnimatedNode {\n  constructor() {\n    super();\n    this._children = [];\n  }\n  __makeNative(platformConfig) {\n    if (!this.__isNative) {\n      this.__isNative = true;\n      for (var _iterator = _createForOfIteratorHelperLoose(this._children), _step; !(_step = _iterator()).done;) {\n        var child = _step.value;\n        child.__makeNative(platformConfig);\n        NativeAnimatedHelper.API.connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n      }\n    }\n    super.__makeNative(platformConfig);\n  }\n  __addChild(child) {\n    if (this._children.length === 0) {\n      this.__attach();\n    }\n    this._children.push(child);\n    if (this.__isNative) {\n      // Only accept \"native\" animated nodes as children\n      child.__makeNative(this.__getPlatformConfig());\n      NativeAnimatedHelper.API.connectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n    }\n  }\n  __removeChild(child) {\n    var index = this._children.indexOf(child);\n    if (index === -1) {\n      console.warn(\"Trying to remove a child that doesn't exist\");\n      return;\n    }\n    if (this.__isNative && child.__isNative) {\n      NativeAnimatedHelper.API.disconnectAnimatedNodes(this.__getNativeTag(), child.__getNativeTag());\n    }\n    this._children.splice(index, 1);\n    if (this._children.length === 0) {\n      this.__detach();\n    }\n  }\n  __getChildren() {\n    return this._children;\n  }\n  __callListeners(value) {\n    super.__callListeners(value);\n    if (!this.__isNative) {\n      for (var _iterator2 = _createForOfIteratorHelperLoose(this._children), _step2; !(_step2 = _iterator2()).done;) {\n        var child = _step2.value;\n        // $FlowFixMe[method-unbinding] added when improving typing for this parameters\n        if (child.__getValue) {\n          child.__callListeners(child.__getValue());\n        }\n      }\n    }\n  }\n}\nexport default AnimatedWithChildren;"], "mappings": "AAUA,YAAY;;AAAC,OAAAA,eAAA;AAAA,OAAAC,YAAA;AAAA,OAAAC,0BAAA;AAAA,OAAAC,eAAA;AAAA,OAAAC,IAAA;AAAA,OAAAC,SAAA;AAAA,SAAAC,WAAAC,CAAA,EAAAC,CAAA,EAAAC,CAAA,WAAAD,CAAA,GAAAL,eAAA,CAAAK,CAAA,GAAAN,0BAAA,CAAAK,CAAA,EAAAG,yBAAA,KAAAC,OAAA,CAAAC,SAAA,CAAAJ,CAAA,EAAAC,CAAA,QAAAN,eAAA,CAAAI,CAAA,EAAAM,WAAA,IAAAL,CAAA,CAAAM,KAAA,CAAAP,CAAA,EAAAE,CAAA;AAAA,SAAAC,0BAAA,cAAAH,CAAA,IAAAQ,OAAA,CAAAC,SAAA,CAAAC,OAAA,CAAAC,IAAA,CAAAP,OAAA,CAAAC,SAAA,CAAAG,OAAA,iCAAAR,CAAA,aAAAG,yBAAA,YAAAA,0BAAA,aAAAH,CAAA;AAAA,SAAAY,cAAAZ,CAAA,EAAAC,CAAA,EAAAC,CAAA,EAAAW,CAAA,QAAAC,CAAA,GAAAjB,IAAA,CAAAD,eAAA,KAAAiB,CAAA,GAAAb,CAAA,CAAAS,SAAA,GAAAT,CAAA,GAAAC,CAAA,EAAAC,CAAA,cAAAW,CAAA,yBAAAC,CAAA,aAAAd,CAAA,WAAAc,CAAA,CAAAP,KAAA,CAAAL,CAAA,EAAAF,CAAA,OAAAc,CAAA;AAEb,OAAOC,+BAA+B,MAAM,uDAAuD;AACnG,OAAOC,YAAY;AACnB,OAAOC,oBAAoB;AAAgC,IACrDC,oBAAoB,aAAAC,aAAA;EACxB,SAAAD,qBAAA,EAAc;IAAA,IAAAE,KAAA;IAAA3B,eAAA,OAAAyB,oBAAA;IACZE,KAAA,GAAArB,UAAA,OAAAmB,oBAAA;IACAE,KAAA,CAAKC,SAAS,GAAG,EAAE;IAAC,OAAAD,KAAA;EACtB;EAACtB,SAAA,CAAAoB,oBAAA,EAAAC,aAAA;EAAA,OAAAzB,YAAA,CAAAwB,oBAAA;IAAAI,GAAA;IAAAC,KAAA,EACD,SAAAC,YAAYA,CAACC,cAAc,EAAE;MAC3B,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;QACpB,IAAI,CAACA,UAAU,GAAG,IAAI;QACtB,KAAK,IAAIC,SAAS,GAAGZ,+BAA+B,CAAC,IAAI,CAACM,SAAS,CAAC,EAAEO,KAAK,EAAE,CAAC,CAACA,KAAK,GAAGD,SAAS,CAAC,CAAC,EAAEE,IAAI,GAAG;UACzG,IAAIC,KAAK,GAAGF,KAAK,CAACL,KAAK;UACvBO,KAAK,CAACN,YAAY,CAACC,cAAc,CAAC;UAClCR,oBAAoB,CAACc,GAAG,CAACC,oBAAoB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEH,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC;QAC9F;MACF;MACArB,aAAA,CAAAM,oBAAA,4BAAmBO,cAAc;IACnC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAW,UAAUA,CAACJ,KAAK,EAAE;MAChB,IAAI,IAAI,CAACT,SAAS,CAACc,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACC,QAAQ,CAAC,CAAC;MACjB;MACA,IAAI,CAACf,SAAS,CAACgB,IAAI,CAACP,KAAK,CAAC;MAC1B,IAAI,IAAI,CAACJ,UAAU,EAAE;QAEnBI,KAAK,CAACN,YAAY,CAAC,IAAI,CAACc,mBAAmB,CAAC,CAAC,CAAC;QAC9CrB,oBAAoB,CAACc,GAAG,CAACC,oBAAoB,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAEH,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC;MAC9F;IACF;EAAC;IAAAX,GAAA;IAAAC,KAAA,EACD,SAAAgB,aAAaA,CAACT,KAAK,EAAE;MACnB,IAAIU,KAAK,GAAG,IAAI,CAACnB,SAAS,CAACoB,OAAO,CAACX,KAAK,CAAC;MACzC,IAAIU,KAAK,KAAK,CAAC,CAAC,EAAE;QAChBE,OAAO,CAACC,IAAI,CAAC,6CAA6C,CAAC;QAC3D;MACF;MACA,IAAI,IAAI,CAACjB,UAAU,IAAII,KAAK,CAACJ,UAAU,EAAE;QACvCT,oBAAoB,CAACc,GAAG,CAACa,uBAAuB,CAAC,IAAI,CAACX,cAAc,CAAC,CAAC,EAAEH,KAAK,CAACG,cAAc,CAAC,CAAC,CAAC;MACjG;MACA,IAAI,CAACZ,SAAS,CAACwB,MAAM,CAACL,KAAK,EAAE,CAAC,CAAC;MAC/B,IAAI,IAAI,CAACnB,SAAS,CAACc,MAAM,KAAK,CAAC,EAAE;QAC/B,IAAI,CAACW,QAAQ,CAAC,CAAC;MACjB;IACF;EAAC;IAAAxB,GAAA;IAAAC,KAAA,EACD,SAAAwB,aAAaA,CAAA,EAAG;MACd,OAAO,IAAI,CAAC1B,SAAS;IACvB;EAAC;IAAAC,GAAA;IAAAC,KAAA,EACD,SAAAyB,eAAeA,CAACzB,KAAK,EAAE;MACrBX,aAAA,CAAAM,oBAAA,+BAAsBK,KAAK;MAC3B,IAAI,CAAC,IAAI,CAACG,UAAU,EAAE;QACpB,KAAK,IAAIuB,UAAU,GAAGlC,+BAA+B,CAAC,IAAI,CAACM,SAAS,CAAC,EAAE6B,MAAM,EAAE,CAAC,CAACA,MAAM,GAAGD,UAAU,CAAC,CAAC,EAAEpB,IAAI,GAAG;UAC7G,IAAIC,KAAK,GAAGoB,MAAM,CAAC3B,KAAK;UAExB,IAAIO,KAAK,CAACqB,UAAU,EAAE;YACpBrB,KAAK,CAACkB,eAAe,CAAClB,KAAK,CAACqB,UAAU,CAAC,CAAC,CAAC;UAC3C;QACF;MACF;IACF;EAAC;AAAA,EAvDgCnC,YAAY;AAyD/C,eAAeE,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}