{"ast": null, "code": "import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nexport var VirtualizedListContext = React.createContext(null);\nif (__DEV__) {\n  VirtualizedListContext.displayName = 'VirtualizedListContext';\n}\nexport function VirtualizedListContextResetter(_ref) {\n  var children = _ref.children;\n  return React.createElement(VirtualizedListContext.Provider, {\n    value: null\n  }, children);\n}\nexport function VirtualizedListContextProvider(_ref2) {\n  var children = _ref2.children,\n    value = _ref2.value;\n  var context = useMemo(function () {\n    return {\n      cellKey: null,\n      getScrollMetrics: value.getScrollMetrics,\n      horizontal: value.horizontal,\n      getOutermostParentListRef: value.getOutermostParentListRef,\n      registerAsNestedChild: value.registerAsNestedChild,\n      unregisterAsNestedChild: value.unregisterAsNestedChild\n    };\n  }, [value.getScrollMetrics, value.horizontal, value.getOutermostParentListRef, value.registerAsNestedChild, value.unregisterAsNestedChild]);\n  return React.createElement(VirtualizedListContext.Provider, {\n    value: context\n  }, children);\n}\nexport function VirtualizedListCellContextProvider(_ref3) {\n  var cellKey = _ref3.cellKey,\n    children = _ref3.children;\n  var currContext = useContext(VirtualizedListContext);\n  var context = useMemo(function () {\n    return currContext == null ? null : _objectSpread(_objectSpread({}, currContext), {}, {\n      cellKey: cellKey\n    });\n  }, [currContext, cellKey]);\n  return React.createElement(VirtualizedListContext.Provider, {\n    value: context\n  }, children);\n}", "map": {"version": 3, "names": ["_objectSpread", "React", "useContext", "useMemo", "__DEV__", "process", "env", "NODE_ENV", "VirtualizedListContext", "createContext", "displayName", "VirtualizedListContextResetter", "_ref", "children", "createElement", "Provider", "value", "VirtualizedListContextProvider", "_ref2", "context", "cellKey", "getScrollMetrics", "horizontal", "getOutermostParentListRef", "registerAsNestedChild", "unregisterAsNestedChild", "VirtualizedListCellContextProvider", "_ref3", "currContext"], "sources": ["C:/Users/<USER>/Music/mobile/node_modules/react-native-web/dist/vendor/react-native/VirtualizedList/VirtualizedListContext.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n * @format\n */\n\nimport * as React from 'react';\nimport { useContext, useMemo } from 'react';\nvar __DEV__ = process.env.NODE_ENV !== 'production';\nexport var VirtualizedListContext = /*#__PURE__*/React.createContext(null);\nif (__DEV__) {\n  VirtualizedListContext.displayName = 'VirtualizedListContext';\n}\n\n/**\n * Resets the context. Intended for use by portal-like components (e.g. Modal).\n */\nexport function VirtualizedListContextResetter(_ref) {\n  var children = _ref.children;\n  return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n    value: null\n  }, children);\n}\n\n/**\n * Sets the context with memoization. Intended to be used by `VirtualizedList`.\n */\nexport function VirtualizedListContextProvider(_ref2) {\n  var children = _ref2.children,\n    value = _ref2.value;\n  // Avoid setting a newly created context object if the values are identical.\n  var context = useMemo(() => ({\n    cellKey: null,\n    getScrollMetrics: value.getScrollMetrics,\n    horizontal: value.horizontal,\n    getOutermostParentListRef: value.getOutermostParentListRef,\n    registerAsNestedChild: value.registerAsNestedChild,\n    unregisterAsNestedChild: value.unregisterAsNestedChild\n  }), [value.getScrollMetrics, value.horizontal, value.getOutermostParentListRef, value.registerAsNestedChild, value.unregisterAsNestedChild]);\n  return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n    value: context\n  }, children);\n}\n\n/**\n * Sets the `cellKey`. Intended to be used by `VirtualizedList` for each cell.\n */\nexport function VirtualizedListCellContextProvider(_ref3) {\n  var cellKey = _ref3.cellKey,\n    children = _ref3.children;\n  // Avoid setting a newly created context object if the values are identical.\n  var currContext = useContext(VirtualizedListContext);\n  var context = useMemo(() => currContext == null ? null : _objectSpread(_objectSpread({}, currContext), {}, {\n    cellKey\n  }), [currContext, cellKey]);\n  return /*#__PURE__*/React.createElement(VirtualizedListContext.Provider, {\n    value: context\n  }, children);\n}"], "mappings": "AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAWhE,OAAO,KAAKC,KAAK,MAAM,OAAO;AAC9B,SAASC,UAAU,EAAEC,OAAO,QAAQ,OAAO;AAC3C,IAAIC,OAAO,GAAGC,OAAO,CAACC,GAAG,CAACC,QAAQ,KAAK,YAAY;AACnD,OAAO,IAAIC,sBAAsB,GAAgBP,KAAK,CAACQ,aAAa,CAAC,IAAI,CAAC;AAC1E,IAAIL,OAAO,EAAE;EACXI,sBAAsB,CAACE,WAAW,GAAG,wBAAwB;AAC/D;AAKA,OAAO,SAASC,8BAA8BA,CAACC,IAAI,EAAE;EACnD,IAAIC,QAAQ,GAAGD,IAAI,CAACC,QAAQ;EAC5B,OAAoBZ,KAAK,CAACa,aAAa,CAACN,sBAAsB,CAACO,QAAQ,EAAE;IACvEC,KAAK,EAAE;EACT,CAAC,EAAEH,QAAQ,CAAC;AACd;AAKA,OAAO,SAASI,8BAA8BA,CAACC,KAAK,EAAE;EACpD,IAAIL,QAAQ,GAAGK,KAAK,CAACL,QAAQ;IAC3BG,KAAK,GAAGE,KAAK,CAACF,KAAK;EAErB,IAAIG,OAAO,GAAGhB,OAAO,CAAC;IAAA,OAAO;MAC3BiB,OAAO,EAAE,IAAI;MACbC,gBAAgB,EAAEL,KAAK,CAACK,gBAAgB;MACxCC,UAAU,EAAEN,KAAK,CAACM,UAAU;MAC5BC,yBAAyB,EAAEP,KAAK,CAACO,yBAAyB;MAC1DC,qBAAqB,EAAER,KAAK,CAACQ,qBAAqB;MAClDC,uBAAuB,EAAET,KAAK,CAACS;IACjC,CAAC;EAAA,CAAC,EAAE,CAACT,KAAK,CAACK,gBAAgB,EAAEL,KAAK,CAACM,UAAU,EAAEN,KAAK,CAACO,yBAAyB,EAAEP,KAAK,CAACQ,qBAAqB,EAAER,KAAK,CAACS,uBAAuB,CAAC,CAAC;EAC5I,OAAoBxB,KAAK,CAACa,aAAa,CAACN,sBAAsB,CAACO,QAAQ,EAAE;IACvEC,KAAK,EAAEG;EACT,CAAC,EAAEN,QAAQ,CAAC;AACd;AAKA,OAAO,SAASa,kCAAkCA,CAACC,KAAK,EAAE;EACxD,IAAIP,OAAO,GAAGO,KAAK,CAACP,OAAO;IACzBP,QAAQ,GAAGc,KAAK,CAACd,QAAQ;EAE3B,IAAIe,WAAW,GAAG1B,UAAU,CAACM,sBAAsB,CAAC;EACpD,IAAIW,OAAO,GAAGhB,OAAO,CAAC;IAAA,OAAMyB,WAAW,IAAI,IAAI,GAAG,IAAI,GAAG5B,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAE4B,WAAW,CAAC,EAAE,CAAC,CAAC,EAAE;MACzGR,OAAO,EAAPA;IACF,CAAC,CAAC;EAAA,GAAE,CAACQ,WAAW,EAAER,OAAO,CAAC,CAAC;EAC3B,OAAoBnB,KAAK,CAACa,aAAa,CAACN,sBAAsB,CAACO,QAAQ,EAAE;IACvEC,KAAK,EAAEG;EACT,CAAC,EAAEN,QAAQ,CAAC;AACd", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}