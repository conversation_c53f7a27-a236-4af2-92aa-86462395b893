{"ast": null, "code": "import * as React from 'react';\nimport NavigationContext from \"./NavigationContext\";\nexport default function useFocusEvents(_ref) {\n  var state = _ref.state,\n    emitter = _ref.emitter;\n  var navigation = React.useContext(NavigationContext);\n  var lastFocusedKeyRef = React.useRef();\n  var currentFocusedKey = state.routes[state.index].key;\n  React.useEffect(function () {\n    return navigation === null || navigation === void 0 ? void 0 : navigation.addListener('focus', function () {\n      lastFocusedKeyRef.current = currentFocusedKey;\n      emitter.emit({\n        type: 'focus',\n        target: currentFocusedKey\n      });\n    });\n  }, [currentFocusedKey, emitter, navigation]);\n  React.useEffect(function () {\n    return navigation === null || navigation === void 0 ? void 0 : navigation.addListener('blur', function () {\n      lastFocusedKeyRef.current = undefined;\n      emitter.emit({\n        type: 'blur',\n        target: currentFocusedKey\n      });\n    });\n  }, [currentFocusedKey, emitter, navigation]);\n  React.useEffect(function () {\n    var lastFocusedKey = lastFocusedKeyRef.current;\n    lastFocusedKeyRef.current = currentFocusedKey;\n    if (lastFocusedKey === undefined && !navigation) {\n      emitter.emit({\n        type: 'focus',\n        target: currentFocusedKey\n      });\n    }\n    if (lastFocusedKey === currentFocusedKey || !(navigation ? navigation.isFocused() : true)) {\n      return;\n    }\n    if (lastFocusedKey === undefined) {\n      return;\n    }\n    emitter.emit({\n      type: 'blur',\n      target: lastFocusedKey\n    });\n    emitter.emit({\n      type: 'focus',\n      target: currentFocusedKey\n    });\n  }, [currentFocusedKey, emitter, navigation]);\n}", "map": {"version": 3, "names": ["React", "NavigationContext", "useFocusEvents", "_ref", "state", "emitter", "navigation", "useContext", "lastFocusedKeyRef", "useRef", "currentFocusedKey", "routes", "index", "key", "useEffect", "addListener", "current", "emit", "type", "target", "undefined", "lastFocused<PERSON>ey", "isFocused"], "sources": ["C:\\Users\\<USER>\\Music\\mobile\\node_modules\\@react-navigation\\core\\src\\useFocusEvents.tsx"], "sourcesContent": ["import type { NavigationState } from '@react-navigation/routers';\nimport * as React from 'react';\n\nimport NavigationContext from './NavigationContext';\nimport type { EventMapCore } from './types';\nimport type { NavigationEventEmitter } from './useEventEmitter';\n\ntype Options<State extends NavigationState> = {\n  state: State;\n  emitter: NavigationEventEmitter<EventMapCore<State>>;\n};\n\n/**\n * Hook to take care of emitting `focus` and `blur` events.\n */\nexport default function useFocusEvents<State extends NavigationState>({\n  state,\n  emitter,\n}: Options<State>) {\n  const navigation = React.useContext(NavigationContext);\n  const lastFocusedKeyRef = React.useRef<string | undefined>();\n\n  const currentFocusedKey = state.routes[state.index].key;\n\n  // When the parent screen changes its focus state, we also need to change child's focus\n  // Coz the child screen can't be focused if the parent screen is out of focus\n  React.useEffect(\n    () =>\n      navigation?.addListener('focus', () => {\n        lastFocusedKeyRef.current = currentFocusedKey;\n        emitter.emit({ type: 'focus', target: currentFocusedKey });\n      }),\n    [currentFocusedKey, emitter, navigation]\n  );\n\n  React.useEffect(\n    () =>\n      navigation?.addListener('blur', () => {\n        lastFocusedKeyRef.current = undefined;\n        emitter.emit({ type: 'blur', target: currentFocusedKey });\n      }),\n    [currentFocusedKey, emitter, navigation]\n  );\n\n  React.useEffect(() => {\n    const lastFocusedKey = lastFocusedKeyRef.current;\n\n    lastFocusedKeyRef.current = currentFocusedKey;\n\n    // We wouldn't have `lastFocusedKey` on initial mount\n    // Fire focus event for the current route on mount if there's no parent navigator\n    if (lastFocusedKey === undefined && !navigation) {\n      emitter.emit({ type: 'focus', target: currentFocusedKey });\n    }\n\n    // We should only emit events when the focused key changed and navigator is focused\n    // When navigator is not focused, screens inside shouldn't receive focused status either\n    if (\n      lastFocusedKey === currentFocusedKey ||\n      !(navigation ? navigation.isFocused() : true)\n    ) {\n      return;\n    }\n\n    if (lastFocusedKey === undefined) {\n      // Only fire events after initial mount\n      return;\n    }\n\n    emitter.emit({ type: 'blur', target: lastFocusedKey });\n    emitter.emit({ type: 'focus', target: currentFocusedKey });\n  }, [currentFocusedKey, emitter, navigation]);\n}\n"], "mappings": "AACA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAOC,iBAAiB;AAYxB,eAAe,SAASC,cAAcA,CAAAC,IAAA,EAGnB;EAAA,IAFjBC,KAAK,GAEUD,IAAA,CAFfC,KAAK;IACLC,OAAA,GACeF,IAAA,CADfE,OAAA;EAEA,IAAMC,UAAU,GAAGN,KAAK,CAACO,UAAU,CAACN,iBAAiB,CAAC;EACtD,IAAMO,iBAAiB,GAAGR,KAAK,CAACS,MAAM,EAAsB;EAE5D,IAAMC,iBAAiB,GAAGN,KAAK,CAACO,MAAM,CAACP,KAAK,CAACQ,KAAK,CAAC,CAACC,GAAG;EAIvDb,KAAK,CAACc,SAAS,CACb;IAAA,OACER,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAAC,OAAO,EAAE,YAAM;MACrCP,iBAAiB,CAACQ,OAAO,GAAGN,iBAAiB;MAC7CL,OAAO,CAACY,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,MAAM,EAAET;MAAkB,CAAC,CAAC;IAC5D,CAAC,CAAC;EAAA,GACJ,CAACA,iBAAiB,EAAEL,OAAO,EAAEC,UAAU,CAAC,CACzC;EAEDN,KAAK,CAACc,SAAS,CACb;IAAA,OACER,UAAU,aAAVA,UAAU,uBAAVA,UAAU,CAAES,WAAW,CAAC,MAAM,EAAE,YAAM;MACpCP,iBAAiB,CAACQ,OAAO,GAAGI,SAAS;MACrCf,OAAO,CAACY,IAAI,CAAC;QAAEC,IAAI,EAAE,MAAM;QAAEC,MAAM,EAAET;MAAkB,CAAC,CAAC;IAC3D,CAAC,CAAC;EAAA,GACJ,CAACA,iBAAiB,EAAEL,OAAO,EAAEC,UAAU,CAAC,CACzC;EAEDN,KAAK,CAACc,SAAS,CAAC,YAAM;IACpB,IAAMO,cAAc,GAAGb,iBAAiB,CAACQ,OAAO;IAEhDR,iBAAiB,CAACQ,OAAO,GAAGN,iBAAiB;IAI7C,IAAIW,cAAc,KAAKD,SAAS,IAAI,CAACd,UAAU,EAAE;MAC/CD,OAAO,CAACY,IAAI,CAAC;QAAEC,IAAI,EAAE,OAAO;QAAEC,MAAM,EAAET;MAAkB,CAAC,CAAC;IAC5D;IAIA,IACEW,cAAc,KAAKX,iBAAiB,IACpC,EAAEJ,UAAU,GAAGA,UAAU,CAACgB,SAAS,EAAE,GAAG,IAAI,CAAC,EAC7C;MACA;IACF;IAEA,IAAID,cAAc,KAAKD,SAAS,EAAE;MAEhC;IACF;IAEAf,OAAO,CAACY,IAAI,CAAC;MAAEC,IAAI,EAAE,MAAM;MAAEC,MAAM,EAAEE;IAAe,CAAC,CAAC;IACtDhB,OAAO,CAACY,IAAI,CAAC;MAAEC,IAAI,EAAE,OAAO;MAAEC,MAAM,EAAET;IAAkB,CAAC,CAAC;EAC5D,CAAC,EAAE,CAACA,iBAAiB,EAAEL,OAAO,EAAEC,UAAU,CAAC,CAAC;AAC9C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}