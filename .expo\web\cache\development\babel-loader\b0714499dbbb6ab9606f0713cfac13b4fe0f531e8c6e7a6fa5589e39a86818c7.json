{"ast": null, "code": "import _classCallCheck from \"@babel/runtime/helpers/classCallCheck\";\nimport _createClass from \"@babel/runtime/helpers/createClass\";\nimport _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\nimport invariant from 'fbjs/lib/invariant';\nvar TaskQueue = function () {\n  function TaskQueue(_ref) {\n    _classCallCheck(this, TaskQueue);\n    var onMoreTasks = _ref.onMoreTasks;\n    this._onMoreTasks = onMoreTasks;\n    this._queueStack = [{\n      tasks: [],\n      popable: true\n    }];\n  }\n  return _createClass(TaskQueue, [{\n    key: \"enqueue\",\n    value: function enqueue(task) {\n      this._getCurrentQueue().push(task);\n    }\n  }, {\n    key: \"enqueueTasks\",\n    value: function enqueueTasks(tasks) {\n      var _this = this;\n      tasks.forEach(function (task) {\n        return _this.enqueue(task);\n      });\n    }\n  }, {\n    key: \"cancelTasks\",\n    value: function cancelTasks(tasksToCancel) {\n      this._queueStack = this._queueStack.map(function (queue) {\n        return _objectSpread(_objectSpread({}, queue), {}, {\n          tasks: queue.tasks.filter(function (task) {\n            return tasksToCancel.indexOf(task) === -1;\n          })\n        });\n      }).filter(function (queue, idx) {\n        return queue.tasks.length > 0 || idx === 0;\n      });\n    }\n  }, {\n    key: \"hasTasksToProcess\",\n    value: function hasTasksToProcess() {\n      return this._getCurrentQueue().length > 0;\n    }\n  }, {\n    key: \"processNext\",\n    value: function processNext() {\n      var queue = this._getCurrentQueue();\n      if (queue.length) {\n        var task = queue.shift();\n        try {\n          if (typeof task === 'object' && task.gen) {\n            this._genPromise(task);\n          } else if (typeof task === 'object' && task.run) {\n            task.run();\n          } else {\n            invariant(typeof task === 'function', 'Expected Function, SimpleTask, or PromiseTask, but got:\\n' + JSON.stringify(task, null, 2));\n            task();\n          }\n        } catch (e) {\n          e.message = 'TaskQueue: Error with task ' + (task.name || '') + ': ' + e.message;\n          throw e;\n        }\n      }\n    }\n  }, {\n    key: \"_getCurrentQueue\",\n    value: function _getCurrentQueue() {\n      var stackIdx = this._queueStack.length - 1;\n      var queue = this._queueStack[stackIdx];\n      if (queue.popable && queue.tasks.length === 0 && stackIdx > 0) {\n        this._queueStack.pop();\n        return this._getCurrentQueue();\n      } else {\n        return queue.tasks;\n      }\n    }\n  }, {\n    key: \"_genPromise\",\n    value: function _genPromise(task) {\n      var _this2 = this;\n      var length = this._queueStack.push({\n        tasks: [],\n        popable: false\n      });\n      var stackIdx = length - 1;\n      var stackItem = this._queueStack[stackIdx];\n      task.gen().then(function () {\n        stackItem.popable = true;\n        _this2.hasTasksToProcess() && _this2._onMoreTasks();\n      }).catch(function (ex) {\n        setTimeout(function () {\n          ex.message = \"TaskQueue: Error resolving Promise in task \" + task.name + \": \" + ex.message;\n          throw ex;\n        }, 0);\n      });\n    }\n  }]);\n}();\nexport default TaskQueue;", "map": {"version": 3, "names": ["_objectSpread", "invariant", "TaskQueue", "_ref", "_classCallCheck", "onMoreTasks", "_onMoreTasks", "_queueStack", "tasks", "popable", "_createClass", "key", "value", "enqueue", "task", "_getCurrentQueue", "push", "enqueueTasks", "_this", "for<PERSON>ach", "cancelTasks", "tasksToCancel", "map", "queue", "filter", "indexOf", "idx", "length", "hasTasksToProcess", "processNext", "shift", "gen", "_genPromise", "run", "JSON", "stringify", "e", "message", "name", "stackIdx", "pop", "_this2", "stackItem", "then", "catch", "ex", "setTimeout"], "sources": ["C:/Users/<USER>/Music/mobile/node_modules/react-native-web/dist/exports/InteractionManager/TaskQueue.js"], "sourcesContent": ["import _objectSpread from \"@babel/runtime/helpers/objectSpread2\";\n/**\n * Copyright (c) <PERSON>.\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n *\n * \n */\n\nimport invariant from 'fbjs/lib/invariant';\nclass TaskQueue {\n  constructor(_ref) {\n    var onMoreTasks = _ref.onMoreTasks;\n    this._onMoreTasks = onMoreTasks;\n    this._queueStack = [{\n      tasks: [],\n      popable: true\n    }];\n  }\n  enqueue(task) {\n    this._getCurrentQueue().push(task);\n  }\n  enqueueTasks(tasks) {\n    tasks.forEach(task => this.enqueue(task));\n  }\n  cancelTasks(tasksToCancel) {\n    this._queueStack = this._queueStack.map(queue => _objectSpread(_objectSpread({}, queue), {}, {\n      tasks: queue.tasks.filter(task => tasksToCancel.indexOf(task) === -1)\n    })).filter((queue, idx) => queue.tasks.length > 0 || idx === 0);\n  }\n  hasTasksToProcess() {\n    return this._getCurrentQueue().length > 0;\n  }\n\n  /**\n   * Executes the next task in the queue.\n   */\n  processNext() {\n    var queue = this._getCurrentQueue();\n    if (queue.length) {\n      var task = queue.shift();\n      try {\n        if (typeof task === 'object' && task.gen) {\n          this._genPromise(task);\n        } else if (typeof task === 'object' && task.run) {\n          task.run();\n        } else {\n          invariant(typeof task === 'function', 'Expected Function, SimpleTask, or PromiseTask, but got:\\n' + JSON.stringify(task, null, 2));\n          task();\n        }\n      } catch (e) {\n        e.message = 'TaskQueue: Error with task ' + (task.name || '') + ': ' + e.message;\n        throw e;\n      }\n    }\n  }\n  _getCurrentQueue() {\n    var stackIdx = this._queueStack.length - 1;\n    var queue = this._queueStack[stackIdx];\n    if (queue.popable && queue.tasks.length === 0 && stackIdx > 0) {\n      this._queueStack.pop();\n      return this._getCurrentQueue();\n    } else {\n      return queue.tasks;\n    }\n  }\n  _genPromise(task) {\n    var length = this._queueStack.push({\n      tasks: [],\n      popable: false\n    });\n    var stackIdx = length - 1;\n    var stackItem = this._queueStack[stackIdx];\n    task.gen().then(() => {\n      stackItem.popable = true;\n      this.hasTasksToProcess() && this._onMoreTasks();\n    }).catch(ex => {\n      setTimeout(() => {\n        ex.message = \"TaskQueue: Error resolving Promise in task \" + task.name + \": \" + ex.message;\n        throw ex;\n      }, 0);\n    });\n  }\n}\nexport default TaskQueue;"], "mappings": ";;AAAA,OAAOA,aAAa,MAAM,sCAAsC;AAWhE,OAAOC,SAAS,MAAM,oBAAoB;AAAC,IACrCC,SAAS;EACb,SAAAA,UAAYC,IAAI,EAAE;IAAAC,eAAA,OAAAF,SAAA;IAChB,IAAIG,WAAW,GAAGF,IAAI,CAACE,WAAW;IAClC,IAAI,CAACC,YAAY,GAAGD,WAAW;IAC/B,IAAI,CAACE,WAAW,GAAG,CAAC;MAClBC,KAAK,EAAE,EAAE;MACTC,OAAO,EAAE;IACX,CAAC,CAAC;EACJ;EAAC,OAAAC,YAAA,CAAAR,SAAA;IAAAS,GAAA;IAAAC,KAAA,EACD,SAAAC,OAAOA,CAACC,IAAI,EAAE;MACZ,IAAI,CAACC,gBAAgB,CAAC,CAAC,CAACC,IAAI,CAACF,IAAI,CAAC;IACpC;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAK,YAAYA,CAACT,KAAK,EAAE;MAAA,IAAAU,KAAA;MAClBV,KAAK,CAACW,OAAO,CAAC,UAAAL,IAAI;QAAA,OAAII,KAAI,CAACL,OAAO,CAACC,IAAI,CAAC;MAAA,EAAC;IAC3C;EAAC;IAAAH,GAAA;IAAAC,KAAA,EACD,SAAAQ,WAAWA,CAACC,aAAa,EAAE;MACzB,IAAI,CAACd,WAAW,GAAG,IAAI,CAACA,WAAW,CAACe,GAAG,CAAC,UAAAC,KAAK;QAAA,OAAIvB,aAAa,CAACA,aAAa,CAAC,CAAC,CAAC,EAAEuB,KAAK,CAAC,EAAE,CAAC,CAAC,EAAE;UAC3Ff,KAAK,EAAEe,KAAK,CAACf,KAAK,CAACgB,MAAM,CAAC,UAAAV,IAAI;YAAA,OAAIO,aAAa,CAACI,OAAO,CAACX,IAAI,CAAC,KAAK,CAAC,CAAC;UAAA;QACtE,CAAC,CAAC;MAAA,EAAC,CAACU,MAAM,CAAC,UAACD,KAAK,EAAEG,GAAG;QAAA,OAAKH,KAAK,CAACf,KAAK,CAACmB,MAAM,GAAG,CAAC,IAAID,GAAG,KAAK,CAAC;MAAA,EAAC;IACjE;EAAC;IAAAf,GAAA;IAAAC,KAAA,EACD,SAAAgB,iBAAiBA,CAAA,EAAG;MAClB,OAAO,IAAI,CAACb,gBAAgB,CAAC,CAAC,CAACY,MAAM,GAAG,CAAC;IAC3C;EAAC;IAAAhB,GAAA;IAAAC,KAAA,EAKD,SAAAiB,WAAWA,CAAA,EAAG;MACZ,IAAIN,KAAK,GAAG,IAAI,CAACR,gBAAgB,CAAC,CAAC;MACnC,IAAIQ,KAAK,CAACI,MAAM,EAAE;QAChB,IAAIb,IAAI,GAAGS,KAAK,CAACO,KAAK,CAAC,CAAC;QACxB,IAAI;UACF,IAAI,OAAOhB,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACiB,GAAG,EAAE;YACxC,IAAI,CAACC,WAAW,CAAClB,IAAI,CAAC;UACxB,CAAC,MAAM,IAAI,OAAOA,IAAI,KAAK,QAAQ,IAAIA,IAAI,CAACmB,GAAG,EAAE;YAC/CnB,IAAI,CAACmB,GAAG,CAAC,CAAC;UACZ,CAAC,MAAM;YACLhC,SAAS,CAAC,OAAOa,IAAI,KAAK,UAAU,EAAE,2DAA2D,GAAGoB,IAAI,CAACC,SAAS,CAACrB,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;YAClIA,IAAI,CAAC,CAAC;UACR;QACF,CAAC,CAAC,OAAOsB,CAAC,EAAE;UACVA,CAAC,CAACC,OAAO,GAAG,6BAA6B,IAAIvB,IAAI,CAACwB,IAAI,IAAI,EAAE,CAAC,GAAG,IAAI,GAAGF,CAAC,CAACC,OAAO;UAChF,MAAMD,CAAC;QACT;MACF;IACF;EAAC;IAAAzB,GAAA;IAAAC,KAAA,EACD,SAAAG,gBAAgBA,CAAA,EAAG;MACjB,IAAIwB,QAAQ,GAAG,IAAI,CAAChC,WAAW,CAACoB,MAAM,GAAG,CAAC;MAC1C,IAAIJ,KAAK,GAAG,IAAI,CAAChB,WAAW,CAACgC,QAAQ,CAAC;MACtC,IAAIhB,KAAK,CAACd,OAAO,IAAIc,KAAK,CAACf,KAAK,CAACmB,MAAM,KAAK,CAAC,IAAIY,QAAQ,GAAG,CAAC,EAAE;QAC7D,IAAI,CAAChC,WAAW,CAACiC,GAAG,CAAC,CAAC;QACtB,OAAO,IAAI,CAACzB,gBAAgB,CAAC,CAAC;MAChC,CAAC,MAAM;QACL,OAAOQ,KAAK,CAACf,KAAK;MACpB;IACF;EAAC;IAAAG,GAAA;IAAAC,KAAA,EACD,SAAAoB,WAAWA,CAAClB,IAAI,EAAE;MAAA,IAAA2B,MAAA;MAChB,IAAId,MAAM,GAAG,IAAI,CAACpB,WAAW,CAACS,IAAI,CAAC;QACjCR,KAAK,EAAE,EAAE;QACTC,OAAO,EAAE;MACX,CAAC,CAAC;MACF,IAAI8B,QAAQ,GAAGZ,MAAM,GAAG,CAAC;MACzB,IAAIe,SAAS,GAAG,IAAI,CAACnC,WAAW,CAACgC,QAAQ,CAAC;MAC1CzB,IAAI,CAACiB,GAAG,CAAC,CAAC,CAACY,IAAI,CAAC,YAAM;QACpBD,SAAS,CAACjC,OAAO,GAAG,IAAI;QACxBgC,MAAI,CAACb,iBAAiB,CAAC,CAAC,IAAIa,MAAI,CAACnC,YAAY,CAAC,CAAC;MACjD,CAAC,CAAC,CAACsC,KAAK,CAAC,UAAAC,EAAE,EAAI;QACbC,UAAU,CAAC,YAAM;UACfD,EAAE,CAACR,OAAO,GAAG,6CAA6C,GAAGvB,IAAI,CAACwB,IAAI,GAAG,IAAI,GAAGO,EAAE,CAACR,OAAO;UAC1F,MAAMQ,EAAE;QACV,CAAC,EAAE,CAAC,CAAC;MACP,CAAC,CAAC;IACJ;EAAC;AAAA;AAEH,eAAe3C,SAAS", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}