// تكوين Firebase
import { initializeApp, getApps, FirebaseApp } from 'firebase/app';
import { getAuth, Auth } from 'firebase/auth';
import { getFirestore, Firestore } from 'firebase/firestore';
import { getStorage, FirebaseStorage } from 'firebase/storage';

// تكوين Firebase للتطوير المحلي - يمكن استخدام Firebase Emulator
const firebaseConfig = {
  // إعدادات للتطوير المحلي - يمكن تحديثها لاحقاً
  apiKey: "demo-api-key",
  authDomain: "shelf-track-demo.firebaseapp.com",
  projectId: "shelf-track-demo",
  storageBucket: "shelf-track-demo.appspot.com",
  messagingSenderId: "123456789",
  appId: "1:123456789:web:demo-app-id",
  measurementId: "G-DEMO1234"
};

// تهيئة Firebase
let app: FirebaseApp;
let auth: Auth;
let db: Firestore;
let storage: FirebaseStorage;

export const initializeFirebase = async () => {
  try {
    // التحقق من عدم تهيئة Firebase مسبقاً
    if (getApps().length === 0) {
      app = initializeApp(firebaseConfig);
    } else {
      app = getApps()[0];
    }

    // تهيئة الخدمات
    auth = getAuth(app);
    db = getFirestore(app);
    storage = getStorage(app);

    console.log('✅ Firebase initialized successfully');
    return { app, auth, db, storage };
  } catch (error) {
    console.error('❌ Firebase initialization error:', error);
    throw error;
  }
};

// تصدير الخدمات
export { auth, db, storage };
export default app;

// مساعدات Firebase
export const getFirebaseApp = () => app;
export const getFirebaseAuth = () => auth;
export const getFirebaseDb = () => db;
export const getFirebaseStorage = () => storage;

// إعدادات Firestore
export const COLLECTIONS = {
  USERS: 'users',
  GROUPS: 'groups',
  PRODUCTS: 'products',
  NOTIFICATIONS: 'notifications',
  INVITES: 'invites',
} as const;

// قواعد الأمان لـ Firestore
export const SECURITY_RULES = {
  // المستخدمون يمكنهم قراءة وكتابة بياناتهم فقط
  users: {
    read: 'auth != null && auth.uid == resource.data.id',
    write: 'auth != null && auth.uid == resource.data.id',
  },
  // أعضاء المجموعة يمكنهم قراءة وكتابة بيانات المجموعة
  groups: {
    read: 'auth != null && auth.uid in resource.data.memberIds',
    write: 'auth != null && (auth.uid == resource.data.createdBy || auth.uid in resource.data.adminIds)',
  },
  // أعضاء المجموعة يمكنهم قراءة وكتابة المنتجات
  products: {
    read: 'auth != null && exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) && auth.uid in get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.memberIds',
    write: 'auth != null && exists(/databases/$(database)/documents/groups/$(resource.data.groupId)) && auth.uid in get(/databases/$(database)/documents/groups/$(resource.data.groupId)).data.memberIds',
  },
};

// إعدادات التخزين المحلي
export const STORAGE_KEYS = {
  USER_DATA: '@shelf_track_user',
  SETTINGS: '@shelf_track_settings',
  OFFLINE_PRODUCTS: '@shelf_track_offline_products',
  LAST_SYNC: '@shelf_track_last_sync',
} as const;

// إعدادات التزامن
export const SYNC_CONFIG = {
  RETRY_ATTEMPTS: 3,
  RETRY_DELAY: 1000, // 1 ثانية
  BATCH_SIZE: 50,
  SYNC_INTERVAL: 5 * 60 * 1000, // 5 دقائق
} as const;
