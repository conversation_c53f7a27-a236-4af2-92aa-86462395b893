import React, { useState } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  Alert,
} from 'react-native';
import {
  Text,
  TextInput,
  Button,
  Card,
  SegmentedButtons,
  useTheme,
  Snackbar,
} from 'react-native-paper';
import { SafeAreaView } from 'react-native-safe-area-context';
import { MaterialIcons } from '@expo/vector-icons';
import DateTimePicker from '@react-native-community/datetimepicker';

const EnhancedAddProductScreen: React.FC = () => {
  const theme = useTheme();
  const [productName, setProductName] = useState('');
  const [barcode, setBarcode] = useState('');
  const [quantity, setQuantity] = useState('1');
  const [selectedLocation, setSelectedLocation] = useState('fridge');
  const [expiryDate, setExpiryDate] = useState(new Date());
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [brand, setBrand] = useState('');
  const [notes, setNotes] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [snackbarVisible, setSnackbarVisible] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');

  const locationOptions = [
    { value: 'fridge', label: '🧊 ثلاجة' },
    { value: 'freezer', label: '❄️ فريزر' },
    { value: 'pantry', label: '🏪 مخزن' },
    { value: 'counter', label: '🏠 منضدة' },
  ];

  const handleDateChange = (event: any, selectedDate?: Date) => {
    setShowDatePicker(false);
    if (selectedDate) {
      setExpiryDate(selectedDate);
    }
  };

  const handleScanBarcode = () => {
    // محاكاة مسح الباركود
    Alert.alert(
      'مسح الباركود',
      'سيتم فتح الكاميرا لمسح الباركود',
      [
        { text: 'إلغاء', style: 'cancel' },
        { 
          text: 'موافق', 
          onPress: () => {
            // محاكاة نتيجة المسح
            setBarcode('123456789012');
            setProductName('منتج ممسوح');
            setBrand('علامة تجارية');
            showSnackbar('تم مسح الباركود بنجاح');
          }
        },
      ]
    );
  };

  const handleSaveProduct = async () => {
    // التحقق من صحة البيانات
    if (!productName.trim()) {
      showSnackbar('يرجى إدخال اسم المنتج');
      return;
    }

    if (!quantity || parseInt(quantity) <= 0) {
      showSnackbar('يرجى إدخال كمية صحيحة');
      return;
    }

    setIsLoading(true);

    try {
      // محاكاة حفظ المنتج
      await new Promise(resolve => setTimeout(resolve, 1500));

      const newProduct = {
        id: Date.now().toString(),
        name: productName.trim(),
        barcode: barcode.trim(),
        quantity: parseInt(quantity),
        location: selectedLocation,
        expiryDate,
        brand: brand.trim(),
        notes: notes.trim(),
        addedAt: new Date(),
        status: calculateStatus(expiryDate),
      };

      console.log('منتج جديد:', newProduct);
      
      // إعادة تعيين النموذج
      resetForm();
      showSnackbar('تم إضافة المنتج بنجاح');
      
    } catch (error) {
      showSnackbar('حدث خطأ أثناء إضافة المنتج');
    } finally {
      setIsLoading(false);
    }
  };

  const calculateStatus = (expiry: Date) => {
    const today = new Date();
    const diffTime = expiry.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    
    if (diffDays < 0) return 'expired';
    if (diffDays <= 3) return 'warning';
    return 'fresh';
  };

  const resetForm = () => {
    setProductName('');
    setBarcode('');
    setQuantity('1');
    setSelectedLocation('fridge');
    setExpiryDate(new Date());
    setBrand('');
    setNotes('');
  };

  const showSnackbar = (message: string) => {
    setSnackbarMessage(message);
    setSnackbarVisible(true);
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.scrollView}>
        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>معلومات المنتج</Text>
            
            {/* اسم المنتج */}
            <TextInput
              label="اسم المنتج *"
              value={productName}
              onChangeText={setProductName}
              style={styles.input}
              mode="outlined"
              placeholder="مثال: حليب نادك كامل الدسم"
            />

            {/* العلامة التجارية */}
            <TextInput
              label="العلامة التجارية"
              value={brand}
              onChangeText={setBrand}
              style={styles.input}
              mode="outlined"
              placeholder="مثال: نادك"
            />

            {/* الباركود */}
            <View style={styles.barcodeContainer}>
              <TextInput
                label="الباركود"
                value={barcode}
                onChangeText={setBarcode}
                style={[styles.input, styles.barcodeInput]}
                mode="outlined"
                placeholder="123456789012"
                keyboardType="numeric"
              />
              <Button
                mode="contained"
                onPress={handleScanBarcode}
                style={styles.scanButton}
                icon="qr-code-scanner"
              >
                مسح
              </Button>
            </View>

            {/* الكمية */}
            <TextInput
              label="الكمية *"
              value={quantity}
              onChangeText={setQuantity}
              style={styles.input}
              mode="outlined"
              keyboardType="numeric"
              placeholder="1"
            />
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>موقع التخزين</Text>
            <SegmentedButtons
              value={selectedLocation}
              onValueChange={setSelectedLocation}
              buttons={locationOptions}
              style={styles.locationButtons}
            />
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>تاريخ انتهاء الصلاحية</Text>
            
            <Button
              mode="outlined"
              onPress={() => setShowDatePicker(true)}
              style={styles.dateButton}
              icon="calendar"
            >
              {expiryDate.toLocaleDateString('ar-SA')}
            </Button>

            {showDatePicker && (
              <DateTimePicker
                value={expiryDate}
                mode="date"
                display="default"
                onChange={handleDateChange}
                minimumDate={new Date()}
              />
            )}

            <View style={styles.statusPreview}>
              <Text style={styles.statusLabel}>حالة المنتج المتوقعة:</Text>
              <View style={[
                styles.statusIndicator,
                { backgroundColor: getStatusColor(calculateStatus(expiryDate)) }
              ]}>
                <Text style={styles.statusText}>
                  {getStatusName(calculateStatus(expiryDate))}
                </Text>
              </View>
            </View>
          </Card.Content>
        </Card>

        <Card style={styles.card}>
          <Card.Content>
            <Text style={styles.sectionTitle}>ملاحظات إضافية</Text>
            <TextInput
              label="ملاحظات"
              value={notes}
              onChangeText={setNotes}
              style={styles.input}
              mode="outlined"
              multiline
              numberOfLines={3}
              placeholder="أي ملاحظات إضافية حول المنتج..."
            />
          </Card.Content>
        </Card>

        <View style={styles.buttonContainer}>
          <Button
            mode="contained"
            onPress={handleSaveProduct}
            loading={isLoading}
            disabled={isLoading}
            style={styles.saveButton}
            icon="check"
          >
            {isLoading ? 'جاري الحفظ...' : 'حفظ المنتج'}
          </Button>
          
          <Button
            mode="outlined"
            onPress={resetForm}
            disabled={isLoading}
            style={styles.resetButton}
            icon="refresh"
          >
            إعادة تعيين
          </Button>
        </View>
      </ScrollView>

      <Snackbar
        visible={snackbarVisible}
        onDismiss={() => setSnackbarVisible(false)}
        duration={3000}
      >
        {snackbarMessage}
      </Snackbar>
    </SafeAreaView>
  );
};

const getStatusColor = (status: string) => {
  switch (status) {
    case 'fresh': return '#4CAF50';
    case 'warning': return '#FF9800';
    case 'expired': return '#F44336';
    default: return '#2196F3';
  }
};

const getStatusName = (status: string) => {
  switch (status) {
    case 'fresh': return 'طازج';
    case 'warning': return 'يحتاج انتباه';
    case 'expired': return 'منتهي الصلاحية';
    default: return status;
  }
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f5f5f5',
  },
  scrollView: {
    flex: 1,
  },
  card: {
    margin: 16,
    marginBottom: 8,
    borderRadius: 12,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 16,
  },
  input: {
    marginBottom: 12,
  },
  barcodeContainer: {
    flexDirection: 'row',
    alignItems: 'flex-end',
    gap: 8,
  },
  barcodeInput: {
    flex: 1,
  },
  scanButton: {
    marginBottom: 12,
  },
  locationButtons: {
    marginBottom: 8,
  },
  dateButton: {
    marginBottom: 16,
  },
  statusPreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  statusLabel: {
    fontSize: 14,
    opacity: 0.7,
  },
  statusIndicator: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 16,
  },
  statusText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
  },
  buttonContainer: {
    margin: 16,
    gap: 12,
  },
  saveButton: {
    paddingVertical: 8,
  },
  resetButton: {
    paddingVertical: 8,
  },
});

export default EnhancedAddProductScreen;
